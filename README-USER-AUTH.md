# ユーザー認証機能実装

## 概要

このドキュメントでは、DDD（Domain-Driven Design）アーキテクチャに基づいて実装されたユーザー認証機能について説明します。

## 実装された機能

### FR1: ユーザーセルフアクティベーション

新規ユーザーが自身のアカウントを有効化する機能です。

**フロー:**
1. ユーザーが社員番号（とオプションでメールアドレス）を入力
2. システムがユーザー情報を検証
3. アクティベーション用トークンを生成してメール送信
4. ユーザーがメールのリンクからパスワードを設定
5. アカウントがアクティブ状態になる

**API エンドポイント:**
- `POST /api/auth/activation/request` - アクティベーション要求
- `POST /api/auth/activation/complete` - パスワード設定とアクティベーション完了

### FR2: ユーザーログイン

有効化済みユーザーがログインする機能です。

**フロー:**
1. ユーザーが社員コードとパスワードを入力
2. システムが認証情報を検証
3. JWT（アクセストークン・リフレッシュトークン）を発行
4. ユーザー情報とトークンを返却

**API エンドポイント:**
- `POST /api/auth/login` - ユーザーログイン

### FR3: パスワードリセット

ユーザーがパスワードを忘れた際のリセット機能です。

**フロー:**
1. ユーザーが社員コードと登録メールアドレスを入力
2. システムがユーザー情報を検証
3. パスワードリセット用トークンを生成してメール送信
4. ユーザーがメールのリンクから新しいパスワードを設定
5. パスワードが更新される

**API エンドポイント:**
- `POST /api/auth/password-reset/request` - パスワードリセット要求
- `POST /api/auth/password-reset/complete` - パスワード更新

### 追加機能

- `GET /api/auth/user/{userId}` - ユーザー情報取得（認証必要）
- `GET /api/auth/health` - ヘルスチェック

## アーキテクチャ

### DDD層構造

```
src/main/java/com/ms/lambda/
├── interfaces/         # インターフェース層
│   └── rest/
│       └── UserAuthController.java
├── application/        # アプリケーション層
│   └── UserAuthApplicationService.java
├── domain/            # ドメイン層
│   └── user/
│       ├── UserActivationService.java
│       ├── UserAuthenticationService.java
│       └── PasswordResetService.java
├── infrastructure/   # インフラストラクチャ層
│   ├── persistence/dao/
│   │   ├── UserDAO.java
│   │   └── UserTokenDAO.java
│   ├── external/email/
│   │   └── EmailService.java
│   └── security/
│       ├── PasswordEncoder.java
│       ├── TokenGenerator.java
│       └── JwtTokenGenerator.java
└── model/            # モデル層
    ├── entity/
    │   ├── User.java (拡張)
    │   └── UserToken.java
    ├── request/
    │   ├── UserActivationRequest.java
    │   ├── UserLoginRequest.java
    │   ├── PasswordResetRequest.java
    │   └── SetPasswordRequest.java
    └── response/
        └── UserAuthResponse.java
```

### 主要コンポーネント

#### ドメイン層
- **UserActivationService**: ユーザーアクティベーションのビジネスロジック
- **UserAuthenticationService**: ユーザー認証のビジネスロジック
- **PasswordResetService**: パスワードリセットのビジネスロジック

#### インフラストラクチャ層
- **PasswordEncoder**: SHA-256ベースのパスワード暗号化
- **TokenGenerator**: セキュアなトークン生成
- **JwtTokenGenerator**: JWT トークン生成
- **EmailService**: Amazon SES を使用したメール送信
- **UserDAO**: ユーザー情報のデータアクセス
- **UserTokenDAO**: トークン情報のデータアクセス

## データベース設計

### 主要テーブル

#### users テーブル（拡張）
```sql
ALTER TABLE users 
ADD COLUMN password_hash VARCHAR(255),
ADD COLUMN employee_code VARCHAR(50) UNIQUE,
ADD COLUMN last_login_at TIMESTAMP,
ADD COLUMN login_attempts INTEGER DEFAULT 0,
ADD COLUMN locked_until TIMESTAMP;
```

#### user_tokens テーブル（新規）
```sql
CREATE TABLE user_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    token_type VARCHAR(50) NOT NULL CHECK (token_type IN ('ACTIVATION', 'PASSWORD_RESET')),
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN NOT NULL DEFAULT FALSE,
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## セキュリティ機能

### パスワード暗号化
- SHA-256 ハッシュ化
- ランダムソルト付き
- パスワード強度チェック

### トークン管理
- セキュアランダム生成
- 有効期限管理（アクティベーション: 24時間、リセット: 1時間）
- 使用済みトークンの無効化

### JWT トークン
- アクセストークン（8時間有効）
- リフレッシュトークン（7日間有効）
- ユーザー情報とロール情報を含む

## 環境変数

```bash
# データベース接続
DB_SECRET_NAME=your-db-secret-name

# JWT設定
JWT_SECRET_KEY=your-jwt-secret-key

# メール設定
DEFAULT_FROM_EMAIL=<EMAIL>

# フロントエンド設定
FRONTEND_BASE_URL=https://your-frontend-domain.com

# AWS設定
AWS_REGION=ap-northeast-1
```

## セットアップ手順

### 1. データベースセットアップ
```bash
# PostgreSQL に接続してスクリプトを実行
psql -h your-db-host -U your-username -d your-database -f database/user_auth_tables.sql
```

### 2. 環境変数設定
必要な環境変数を設定してください。

### 3. メール設定
Amazon SES でメール送信を設定してください。

## API 使用例

### ユーザーアクティベーション要求
```bash
curl -X POST https://your-api-domain/api/auth/activation/request \
  -H "Content-Type: application/json" \
  -d '{
    "employeeCode": "EMP001",
    "email": "<EMAIL>"
  }'
```

### ユーザーログイン
```bash
curl -X POST https://your-api-domain/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "employeeCode": "EMP001",
    "password": "your-password"
  }'
```

### パスワードリセット要求
```bash
curl -X POST https://your-api-domain/api/auth/password-reset/request \
  -H "Content-Type: application/json" \
  -d '{
    "employeeCode": "EMP001",
    "email": "<EMAIL>"
  }'
```

## テスト

### 単体テスト
各ドメインサービスとDAOクラスの単体テストを作成することを推奨します。

### 統合テスト
実際のデータベースとメールサービスを使用した統合テストを実行してください。

## 運用考慮事項

### ログ監視
- 認証失敗の監視
- 異常なトークン使用の検出
- パスワードリセット頻度の監視

### メンテナンス
- 期限切れトークンの定期削除
- ユーザーセッションの管理
- パスワードポリシーの適用

### セキュリティ
- ブルートフォース攻撃対策
- レート制限の実装
- 監査ログの記録

## 今後の拡張

- 多要素認証（MFA）
- ソーシャルログイン
- シングルサインオン（SSO）
- パスワードレス認証

## トラブルシューティング

### よくある問題

1. **メールが送信されない**
   - Amazon SES の設定を確認
   - 送信者メールアドレスの検証状況を確認

2. **トークンが無効**
   - トークンの有効期限を確認
   - データベースのトークン状態を確認

3. **認証に失敗する**
   - ユーザーのステータスを確認
   - パスワードハッシュの整合性を確認

## 参考資料

- [DDD アーキテクチャガイド](README-DDD.md)
- [AWS Lambda 開発ガイド](https://docs.aws.amazon.com/lambda/)
- [Amazon SES 開発者ガイド](https://docs.aws.amazon.com/ses/)
- [JWT 仕様](https://tools.ietf.org/html/rfc7519)
