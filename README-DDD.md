# DDD（ドメイン駆動設計）実装ガイド

## 概要

本プロジェクトは、DDD（Domain-Driven Design：ドメイン駆動設計）の原則に基づいて設計・実装されています。

## アーキテクチャ構成

### レイヤー構成

```
src/main/java/com/ms/bp/
├── interfaces/         # インターフェース層（プレゼンテーション層）
│   ├── rest/           # REST API コントローラー
│   └── lambda         # AWS Lambda ハンドラー
├── application/        # アプリケーション層
│   ├── ExportJobStatusService.java
│   └── ImportJobStatusService.java
├── domain/            # ドメイン層
│   ├── file/  # インポート・エクスポートドメイン
│   │   ├── base/      # 抽象サービスクラス
│   │   └── user/      # ユーザーインポート・エクスポート
│   └── user/          # ユーザードメイン（認証関連）
├── infrastructure/   # インフラストラクチャ層
│   ├── persistence/   # データ永続化
│   │   └── dao/       # データアクセスオブジェクト
│   └── external/      # 外部サービス連携
│       └── s3/        # S3サービス
├── model/            # モデル（エンティティ・DTO）
│   ├── entity/       # エンティティクラス
│   ├── dto/          # データ転送オブジェクト
│   ├── request/      # リクエストモデル
│   └── response/     # レスポンスモデル
└── shared/           # 共有ユーティリティ
    ├── common/       # 共通機能
    └── util/         # ユーティリティクラス
```

## 各層の責務

### 1. インターフェース層（Interfaces Layer）

**責務**: 外部からのリクエストを受け取り、適切なアプリケーションサービスに処理を委譲する

**主要コンポーネント**:
- `DataController`: REST APIエンドポイント
- `LambdaHandler`: AWS Lambda関数のエントリーポイント

**実装例**:
```java
@RestController
public class DataController {
    private final ExportJobStatusService exportService;
    
    public CommonResult<?> exportData(APIGatewayProxyRequestEvent request, 
                                    UserInfo userInfo, Context context) {
        // リクエスト検証
        ExportRequest exportRequest = objectMapper.readValue(request.getBody(), ExportRequest.class);
        validateExportRequest(exportRequest);
        
        // アプリケーションサービスへの委譲
        AbstractExportService<?> exportService = getExportService(exportRequest.getDataType());
        
        // 非同期処理の開始
        String jobId = generateJobId(exportRequest.getDataType());
        // ...
    }
}
```

### 2. アプリケーション層（Application Layer）

**責務**: ビジネスユースケースを調整し、ドメインオブジェクトを組み合わせて処理を実行する

**主要コンポーネント**:
- `ExportJobStatusService`: エクスポートジョブの管理
- `ImportJobStatusService`: インポートジョブの管理

**特徴**:
- ドメインロジックは含まない
- トランザクション境界を定義
- ドメインサービスとDAOを組み合わせて使用

**実装例**:
```java
@Service
public class ExportJobStatusService {
    
    public ExportJobStatus createJob(String jobId, String dataType, String userId, 
                                   String userName, Map<String, Object> filters) {
        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {
            
            ExportJobStatusDAO dao = new ExportJobStatusDAO(jdbcTemplate);
            
            // ドメインオブジェクトの作成
            ExportJobStatus jobStatus = new ExportJobStatus(jobId, dataType, "PENDING", userId, userName);
            jobStatus.setFilters(filters);
            
            // 永続化
            dao.insert(jobStatus);
            
            return jobStatus;
        }
    }
}
```

### 3. ドメイン層（Domain Layer）

**責務**: ビジネスロジックとビジネスルールを実装する

#### 3.1 ドメインサービス

**抽象サービスクラス**:
- `AbstractDataService`: データ処理の共通テンプレート
- `AbstractImportService`: インポート処理の共通ロジック
- `AbstractExportService`: エクスポート処理の共通ロジック

**実装例**:
```java
public abstract class AbstractImportService<T> extends AbstractDataService<InputStream, ImportResult> {
    
    // テンプレートメソッドパターンによる共通処理フロー
    @Override
    protected ImportResult doExecute(InputStream input, Map<String, Object> params) throws Exception {
        // オプション構築
        ImportOptions options = buildImportOptions();
        
        // データ検証器を取得
        DataValidator validator = getDataValidator();
        
        // インポート実行（検証器は必須）
        return facade.importData(
                input,
                getDTOClass(),
                getDTOConverter(),
                options,
                validator
        );
    }
    
    // 抽象メソッド（サブクラスで実装）
    protected abstract Class<T> getDTOClass();
    protected abstract DTOConverter<T> getDTOConverter();
    protected abstract DataValidator getDataValidator();
}
```

#### 3.2 具体的なドメインサービス

**ユーザーインポートサービス**:
```java
public class UserImportService extends AbstractImportService<UserImportDTO> {
    
    @Override
    protected String getDataType() {
        return "USERS";
    }
    
    @Override
    protected DataValidator getDataValidator() {
        // DTOベースのバリデーターを使用（アノテーション検証 + カスタム検証）
        return new DTODataValidator<>(UserImportDTO.class, this::validateCustomLogic);
    }
    
    @Override
    protected Class<UserImportDTO> getDTOClass() {
        return UserImportDTO.class;
    }
    
    @Override
    protected DTOConverter<UserImportDTO> getDTOConverter() {
        return new UserImportDTOConverter();
    }
    
    // カスタムビジネスロジック検証
    private List<ValidationError> validateCustomLogic(UserImportDTO dto, int rowIndex) {
        List<ValidationError> errors = new ArrayList<>();
        
        // 重複チェック
        if (!dto.isNewUser() && !isValidUserForUpdate(dto)) {
            errors.add(new ValidationError("ユーザーID", "更新対象のユーザーが存在しません", rowIndex));
        }
        
        return errors;
    }
}
```

### 4. インフラストラクチャ層（Infrastructure Layer）

**責務**: 外部システムとの連携とデータ永続化を担当する

#### 4.1 データアクセスオブジェクト（DAO）

**実装クラス**:
- `ExportJobStatusDAO`: PostgreSQLを使用したエクスポートジョブステータス永続化
- `ImportJobStatusDAO`: PostgreSQLを使用したインポートジョブステータス永続化

**実装例**:
```java
public class ExportJobStatusDAO {
    private final JdbcTemplate jdbcTemplate;
    
    public void insert(ExportJobStatus jobStatus) throws SQLException {
        String sql = """
            INSERT INTO export_job_status (
                job_id, data_type, status, message, user_id, user_name,
                filters, areas, request_params, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?::jsonb, ?, ?::jsonb, ?, ?)
            """;
        
        Object[] params = {
            jobStatus.getJobId(),
            jobStatus.getDataType(),
            jobStatus.getStatus(),
            // ... その他のパラメータ
        };
        
        int result = jdbcTemplate.update(sql, params);
        if (result != 1) {
            throw new SQLException("エクスポートジョブステータスの挿入に失敗しました");
        }
    }
}
```

#### 4.2 外部サービス連携

**S3サービス**:
```java
public class S3Service {
    private final S3Client s3Client;
    
    public ResponseInputStream<GetObjectResponse> getInputStreamFromS3Url(String key) throws IOException {
        try {
            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(this.bucketName)
                    .key(key)
                    .build();
            
            return s3Client.getObject(request);
        } catch (Exception e) {
            throw new IOException("S3からのInputStream取得に失敗しました: " + e.getMessage(), e);
        }
    }
}
```

## DDDパターンの実装

### 1. テンプレートメソッドパターン

**目的**: 共通処理フローを定義し、具体的な処理を子クラスで実装する

**実装**:
```java
public abstract class AbstractDataService<T, R> {
    
    // テンプレートメソッド
    public final R execute(T input, Map<String, Object> params) {
        try {
            // 前処理
            preProcess(input, params);
            
            // パラメータ検証
            validateParameters(input, params);
            
            // ログ出力
            logStart(input, params);
            
            // メイン処理（抽象メソッド）
            long startTime = System.currentTimeMillis();
            R result = doExecute(input, params);
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 後処理
            postProcess(result, executionTime);
            
            return result;
        } catch (Exception e) {
            // エラーハンドリング
            handleError(e);
            throw new ServiceException("処理中にエラーが発生しました", e);
        }
    }
    
    // 抽象メソッド（サブクラスで実装）
    protected abstract R doExecute(T input, Map<String, Object> params) throws Exception;
    protected abstract void validateParameters(T input, Map<String, Object> params);
}
```

### 2. DTOパターン（ドメインオブジェクトとして）

**目的**: レイヤー間のデータ転送とビジネスロジックの実装

**実装**:
```java
@Data
public class UserImportDTO {
    
    // アノテーションベースの検証
    @Required(message = "ユーザー名は必須です")
    @MinLength(value = 3, message = "ユーザー名は3文字以上で指定してください")
    @DatabaseField("username")
    private String username;
    
    @Required(message = "メールアドレスは必須です")
    @Email(message = "無効なメールアドレス形式です")
    @DatabaseField("email")
    private String email;
    
    // ビジネスメソッド
    public boolean isNewUser() {
        return id == null;
    }
    
    public String getNormalizedEmail() {
        return email != null ? email.toLowerCase().trim() : null;
    }
    
    public boolean requiresDepartmentValidation() {
        return departmentId != null;
    }
    
    public List<String> validateBasicIntegrity() {
        List<String> errors = new ArrayList<>();
        
        if (username == null || username.trim().isEmpty()) {
            errors.add("ユーザー名は必須です");
        }
        
        if (email == null || email.trim().isEmpty()) {
            errors.add("メールアドレスは必須です");
        }
        
        return errors;
    }
}
```

### 3. ファサードパターン

**目的**: 複雑なサブシステムへの統一されたインターフェースを提供

**実装**:
```java
public class ImportExportFacade {

    public ImportResult importData(InputStream inputStream,
                                 Class<?> dtoClass,
                                 DTOConverter<?> converter,
                                 ImportOptions options,
                                 DataValidator validator) {
        // CSV読み込み → Map変換 → DTO変換 → 検証 → データベース保存
        // の複雑な処理フローを統一されたインターフェースで提供
    }

    public ExportResult exportData(String sql,
                                 Object[] params,
                                 DataFormatter formatter,
                                 ExportOptions options) {
        // データベース取得 → Map変換 → CSV出力 → S3アップロード
        // の複雑な処理フローを統一されたインターフェースで提供
    }
}
```

## エンティティとドメインオブジェクト

### 1. エンティティクラス

**ExportJobStatus**:
```java
@Data
public class ExportJobStatus {
    // 基本情報
    private String jobId;
    private String dataType;
    private String status;
    private String userId;
    private String userName;

    // ビジネスメソッド
    public boolean isCompleted() {
        return "COMPLETED".equals(this.status);
    }

    public boolean isDownloadable() {
        return isCompleted() && downloadUrl != null && !downloadUrl.isEmpty();
    }

    public void updateCompletionStats(String s3Key, String fileName, String downloadUrl,
                                    long fileSize, int totalRecords, int exportedRecords) {
        this.outputS3Key = s3Key;
        this.outputFileName = fileName;
        this.downloadUrl = downloadUrl;
        // ... 統計情報の更新
        this.completedAt = new Date();
        this.updatedAt = new Date();
    }
}
```

**ImportJobStatus**:
```java
@Data
public class ImportJobStatus {
    // 基本情報
    private String jobId;
    private String dataType;
    private String status;
    private String s3Key;

    // 処理結果統計
    private Integer totalCount;
    private Integer processedCount;
    private Integer insertedCount;
    private Integer updatedCount;
    private Integer failedCount;

    // ビジネスメソッド
    public boolean isProcessing() {
        return "PROCESSING".equals(this.status);
    }

    public double getSuccessRate() {
        if (processedCount == null || processedCount == 0) {
            return 0.0;
        }
        int successCount = (insertedCount != null ? insertedCount : 0) +
                          (updatedCount != null ? updatedCount : 0);
        return (double) successCount / processedCount * 100;
    }
}
```

### 2. ユーザー情報（認証ドメイン）

**UserInfo**:
```java
@Data
public class UserInfo implements Serializable {
    private String oid;           // Azure AD オブジェクトID
    private String name;          // ユーザー名
    private String preferredUsername; // UPN
    private String mail;          // メールアドレス

    // 認証関連のビジネスロジック
    public boolean isValidUser() {
        return oid != null && !oid.trim().isEmpty();
    }

    public String getDisplayName() {
        return name != null ? name : preferredUsername;
    }
}
```

## 依存関係の方向

DDDでは、依存関係の方向が重要です：

```
インターフェース層 → アプリケーション層 → ドメイン層 ← インフラストラクチャ層
```

- **ドメイン層**: 他の層に依存しない（純粋なビジネスロジック）
- **アプリケーション層**: ドメイン層とインフラストラクチャ層に依存
- **インフラストラクチャ層**: ドメイン層のインターフェースを実装（依存関係逆転）
- **インターフェース層**: アプリケーション層に依存