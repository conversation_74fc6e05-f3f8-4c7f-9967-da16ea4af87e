-- ユーザー認証機能用のデータベーステーブル定義
-- PostgreSQL用のDDLスクリプト

-- 1. usersテーブルの拡張（認証関連フィールドを追加）
-- 既存のusersテーブルに認証用のカラムを追加
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255),
ADD COLUMN IF NOT EXISTS employee_code VARCHAR(50) UNIQUE,
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP;

-- employee_codeにインデックスを作成（ログイン時の検索性能向上）
CREATE INDEX IF NOT EXISTS idx_users_employee_code ON users(employee_code);

-- emailにインデックスを作成（パスワードリセット時の検索性能向上）
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- statusにインデックスを作成（アクティブユーザーの検索性能向上）
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- 2. user_tokensテーブルの作成
-- アクティベーションとパスワードリセット用のトークンを管理
CREATE TABLE IF NOT EXISTS user_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    token_type VARCHAR(50) NOT NULL CHECK (token_type IN ('ACTIVATION', 'PASSWORD_RESET')),
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN NOT NULL DEFAULT FALSE,
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外部キー制約
    CONSTRAINT fk_user_tokens_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- user_tokensテーブルのインデックス
CREATE INDEX IF NOT EXISTS idx_user_tokens_token ON user_tokens(token);
CREATE INDEX IF NOT EXISTS idx_user_tokens_user_id ON user_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tokens_type_expires ON user_tokens(token_type, expires_at);
CREATE INDEX IF NOT EXISTS idx_user_tokens_user_type_used ON user_tokens(user_id, token_type, used);

-- 3. departmentsテーブルの作成（存在しない場合）
-- ユーザーの部門情報を管理
CREATE TABLE IF NOT EXISTS departments (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE,
    description TEXT,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- departmentsテーブルのインデックス
CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code);
CREATE INDEX IF NOT EXISTS idx_departments_active ON departments(active);

-- 4. rolesテーブルの作成（存在しない場合）
-- ユーザーの役割情報を管理
CREATE TABLE IF NOT EXISTS roles (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) UNIQUE,
    description TEXT,
    permissions TEXT[], -- 権限のリスト（JSON配列として格納）
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- rolesテーブルのインデックス
CREATE INDEX IF NOT EXISTS idx_roles_code ON roles(code);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(active);

-- 5. user_sessionsテーブルの作成（将来の拡張用）
-- ユーザーセッション管理（オプション）
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    refresh_token VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外部キー制約
    CONSTRAINT fk_user_sessions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- user_sessionsテーブルのインデックス
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);

-- 6. 初期データの挿入

-- デフォルト部門の作成
INSERT INTO departments (name, code, description) VALUES
('システム管理部', 'SYS', 'システム管理を担当する部門'),
('営業部', 'SALES', '営業活動を担当する部門'),
('開発部', 'DEV', 'システム開発を担当する部門'),
('人事部', 'HR', '人事管理を担当する部門')
ON CONFLICT (code) DO NOTHING;

-- デフォルト役割の作成
INSERT INTO roles (name, code, description, permissions) VALUES
('システム管理者', 'ADMIN', 'システム全体の管理権限', ARRAY['ALL']),
('マネージャー', 'MANAGER', '部門管理権限', ARRAY['USER_MANAGE', 'DATA_EXPORT', 'DATA_IMPORT']),
('一般ユーザー', 'USER', '基本的な操作権限', ARRAY['DATA_VIEW', 'DATA_EXPORT']),
('ゲスト', 'GUEST', '閲覧のみの権限', ARRAY['DATA_VIEW'])
ON CONFLICT (code) DO NOTHING;

-- 7. トリガー関数の作成（updated_atの自動更新）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. トリガーの作成
-- usersテーブル
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- user_tokensテーブル
DROP TRIGGER IF EXISTS update_user_tokens_updated_at ON user_tokens;
CREATE TRIGGER update_user_tokens_updated_at
    BEFORE UPDATE ON user_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- departmentsテーブル
DROP TRIGGER IF EXISTS update_departments_updated_at ON departments;
CREATE TRIGGER update_departments_updated_at
    BEFORE UPDATE ON departments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- rolesテーブル
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON roles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 9. サンプルユーザーデータの作成（開発・テスト用）
-- 注意: 本番環境では削除してください
INSERT INTO users (username, email, full_name, employee_code, department_id, role_id, status, area) VALUES
('admin', '<EMAIL>', '管理者', 'EMP001', 1, 1, 'INACTIVE', 'TOKYO'),
('manager1', '<EMAIL>', '田中太郎', 'EMP002', 2, 2, 'INACTIVE', 'TOKYO'),
('user1', '<EMAIL>', '佐藤花子', 'EMP003', 3, 3, 'INACTIVE', 'OSAKA'),
('guest1', '<EMAIL>', '山田次郎', 'EMP004', 4, 4, 'INACTIVE', 'TOKYO')
ON CONFLICT (employee_code) DO NOTHING;

-- 10. 期限切れトークンのクリーンアップ用関数
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 11. 統計情報取得用ビュー
CREATE OR REPLACE VIEW user_auth_stats AS
SELECT 
    'total_users' as metric,
    COUNT(*) as value
FROM users
UNION ALL
SELECT 
    'active_users' as metric,
    COUNT(*) as value
FROM users 
WHERE status = 'ACTIVE'
UNION ALL
SELECT 
    'inactive_users' as metric,
    COUNT(*) as value
FROM users 
WHERE status = 'INACTIVE'
UNION ALL
SELECT 
    'pending_activations' as metric,
    COUNT(*) as value
FROM user_tokens 
WHERE token_type = 'ACTIVATION' AND used = FALSE AND expires_at > CURRENT_TIMESTAMP
UNION ALL
SELECT 
    'pending_resets' as metric,
    COUNT(*) as value
FROM user_tokens 
WHERE token_type = 'PASSWORD_RESET' AND used = FALSE AND expires_at > CURRENT_TIMESTAMP;

-- 12. コメントの追加
COMMENT ON TABLE user_tokens IS 'ユーザーアクティベーションとパスワードリセット用のトークン管理テーブル';
COMMENT ON COLUMN user_tokens.token_type IS 'トークンタイプ: ACTIVATION（アクティベーション）, PASSWORD_RESET（パスワードリセット）';
COMMENT ON COLUMN user_tokens.expires_at IS 'トークンの有効期限';
COMMENT ON COLUMN user_tokens.used IS 'トークンが使用済みかどうか';

COMMENT ON TABLE departments IS '部門情報管理テーブル';
COMMENT ON TABLE roles IS 'ユーザー役割管理テーブル';
COMMENT ON TABLE user_sessions IS 'ユーザーセッション管理テーブル（将来の拡張用）';

-- スクリプト完了メッセージ
DO $$
BEGIN
    RAISE NOTICE 'ユーザー認証機能用のデータベーステーブルの作成が完了しました。';
    RAISE NOTICE '作成されたテーブル: users (拡張), user_tokens, departments, roles, user_sessions';
    RAISE NOTICE 'サンプルデータが挿入されました。本番環境では削除してください。';
END $$;
