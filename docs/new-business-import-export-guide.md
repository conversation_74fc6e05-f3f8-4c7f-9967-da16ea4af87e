# インポート・エクスポート実装ガイド

## 概要

新規業務のインポート・エクスポート機能を実装する方法を説明します。

## エクスポート機能実装

### 1. エクスポートサービスクラスの作成

```java
package com.ms.bp.domain.importexport.

[business];

import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class [BusinessName]ExportService extends AbstractExportService<Map<String, Object>>{

@Override
protected String getDataType() {
    return "[BUSINESS_NAME]"; // 例: "PRODUCTS", "ORDERS"
}

@Override
protected Map<String, Object> formatData(Map<String, Object> rawData) {
    // オプション：データフォーマット処理
    // データベースから取得したMapデータを最終出力用に格式化する
    Map<String, Object> formattedData = new HashMap<>(rawData);
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 日付フィールドのフォーマット
    if (formattedData.get("created_at") instanceof Date) {
        formattedData.put("created_at", dateFormat.format((Date) formattedData.get("created_at")));
    }
    if (formattedData.get("updated_at") instanceof Date) {
        formattedData.put("updated_at", dateFormat.format((Date) formattedData.get("updated_at")));
    }

    return formattedData;
}

@Override
protected ExportOptions buildExportOptions() {
    String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());

    return ExportOptions.builder()
            .format(FileFormat.CSV)
            .includeHeader(true)
            .columns(Arrays.asList(
                    // SQLクエリフィールド順序と一致することを確保
                    "id",
                    "field1",
                    "field2",
                    // ... その他のフィールド
                    "created_at",
                    "updated_at"
            ))
            .batchSize(1000)
            .countTotal(true)
            .s3Bucket(System.getenv("S3_BUCKET_NAME"))
            .s3Key("exports/[business]/[business]_" + timestamp + ".csv")
            .build();
}

@Override
protected SqlWithParams buildQuery(Map<String, Object> criteria) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();

    // 基本SELECT文（フィールド順序はcolumnsと一致する必要がある）
    sql.append("SELECT ");
    sql.append("t.id, ");
    sql.append("t.field1, ");
    sql.append("t.field2, ");
    // ... その他のフィールド
    sql.append("t.created_at, ");
    sql.append("t.updated_at ");
    sql.append("FROM [table_name] t ");

    // WHERE条件の構築
    List<String> conditions = new ArrayList<>();
    if (criteria != null && !criteria.isEmpty()) {
        // 検索条件の追加（AbstractExportServiceの共通ユーティリティを使用）
        addCondition(conditions, params, "t.field1", criteria.get("field1"), "=");
        addCondition(conditions, params, "t.field2", criteria.get("field2"), "LIKE");
        // ... その他の条件
    }

    if (!conditions.isEmpty()) {
        sql.append("WHERE ");
        sql.append(String.join(" AND ", conditions));
    }

    sql.append(" ORDER BY t.id");

    // パラメータ変換（Map形式で返す）
    Map<String, Object> paramMap = new HashMap<>();
    for (int i = 0; i < params.size(); i++) {
        paramMap.put("param" + i, params.get(i));
    }

    return new SqlWithParams(sql.toString(), paramMap);
}

/**
 * 追加オプションを適用（必要に応じてオーバーライド）
 */
@Override
protected void applyAdditionalOptions(ExportOptions options, Map<String, Object> params) {
    // パラメータから動的にオプションを調整
    // 例：バッチサイズの調整、S3パスの変更など
}
}
```

### 2. データ展開処理器の実装（オプション）

データベースの1行を複数行に展開する必要がある場合は、データ展開処理器を実装します。

```java
package com.ms.bp.domain.importexport.

[business];

import com.ms.bp.domain.file.base.DataExpander;

/**
 * [BusinessName]データ展開処理器
 * 1行のデータを複数行に展開する業務ロジックを実装
 */
public class [BusinessName]DataExpander implements

        DataExpander {

            @Override
            public List<Map<String, Object>> expandData (Map < String, Object > rawData, ExpansionContext context){
                List<Map<String, Object>> expandedRows = new ArrayList<>();

                // 展開対象フィールドを取得（例：カンマ区切りの値）
                String multiValues = (String) rawData.get("multi_field");

                if (multiValues != null && !multiValues.trim().isEmpty()) {
                    String[] values = multiValues.split(",");

                    // 各値に対して行を作成
                    for (String value : values) {
                        Map<String, Object> expandedRow = new HashMap<>(rawData);
                        expandedRow.put("single_field", value.trim());
                        expandedRow.remove("multi_field"); // 元のフィールドを削除
                        expandedRows.add(expandedRow);
                    }
                } else {
                    // 展開対象がない場合は元データをそのまま返す
                    expandedRows.add(new HashMap<>(rawData));
                }

                return expandedRows;
            }

            @Override
            public boolean needsExpansion (Map < String, Object > rawData){
                String multiValues = (String) rawData.get("multi_field");
                return multiValues != null && multiValues.contains(",");
            }

            @Override
            public String getDescription () {
                return "[BusinessName]データ展開処理器（複数値を各行に分割）";
            }
        }
```

### 3. エクスポートサービスでの展開処理器の使用

```java
public class [BusinessName]ExportService extends AbstractExportService<Map<String, Object>>{

    @Override
    protected DataExpander getDataExpander() {
        // データ展開が必要な場合は展開処理器を返す
        return new [BusinessName]DataExpander();

        // データ展開が不要な場合はnullを返す
        // return null;
    }

    @Override
    protected SqlWithParams buildQuery(Map<String, Object> criteria) {
        // データ展開対応：複数値を集約して取得
        sql.append("SELECT ");
        sql.append("t.id, ");
        sql.append("t.field1, ");
        sql.append("GROUP_CONCAT(related.value ORDER BY related.value SEPARATOR ',') AS multi_field, ");
        sql.append("t.created_at ");
        sql.append("FROM [table_name] t ");
        sql.append("LEFT JOIN [related_table] related ON t.id = related.parent_id ");

        // GROUP BY句を追加（集約クエリのため）
        sql.append("GROUP BY t.id, t.field1, t.created_at ");
        sql.append("ORDER BY t.id");

        // ... 残りの実装
    }
}
```

### 4. エクスポート機能の注意点

#### A. SQLフィールド順序
- **SQLクエリフィールド順序とCSV columnsが完全に一致することを必須とする**
- フィールドエイリアスを使用してデータベースフィールド名とCSVヘッダーを一致させる

#### B. データフォーマット
- `formatData`メソッドでのみ必要なフォーマット処理を行う
- 複雑なビジネスロジック処理を避ける
- 一般的なフォーマット：日付、数値、列挙値の変換

#### C. データ展開処理
- **1行→多行変換**：`DataExpander`インターフェースを実装
- **集約クエリ**：`GROUP_CONCAT`等を使用して複数値を1つのフィールドに集約
- **パフォーマンス制御**：`maxExpandedRows`で展開行数を制限
- **エラー処理**：展開エラー時は元データを保持して処理継続

#### D. パフォーマンス考慮事項
- 適切な`batchSize`を設定（推奨1000）
- `ORDER BY`を使用してデータ順序の一貫性を確保
- SQLで複雑なサブクエリの使用を避ける
- データ展開時のメモリ使用量に注意

## インポート機能実装

### 1. DTOクラスの作成

```java
package com.ms.bp.model.dto;

import com.ms.bp.shared.common.io.annotation.DatabaseField;
import com.ms.bp.shared.common.io.annotation.DatabaseField.AutoGenerateType;
import com.ms.bp.shared.common.io.validation.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * [BusinessName]インポート用DTO
 * アノテーションベースの検証を使用
 * 領域オブジェクトとして業務ロジックも含む
 * パフォーマンス最適化：DatabaseMappableインターフェースを実装
 */
@Data
public class [BusinessName]ImportDTO implements

        DatabaseMappable {

            /**
             * ID（更新時使用）
             */
            @Range(min = 1, message = "IDは1以上の数値で指定してください")
            private Long id;

            /**
             * 必須フィールドの例
             */
            @Required(message = "フィールド1は必須です")
            @MaxLength(value = 100, message = "フィールド1は100文字以内で指定してください")
            private String field1;

            /**
             * オプションフィールドの例
             */
            @MaxLength(value = 50, message = "フィールド2は50文字以内で指定してください")
            private String field2;

            /**
             * 外部キーフィールドの例
             */
            @Range(min = 1, message = "関連IDは1以上の数値で指定してください")
            private Long relatedId;

            /**
             * ステータスフィールドの例
             */
            @ValidValues(value = {"ACTIVE", "INACTIVE"}, ignoreCase = true,
                    message = "ステータスはACTIVE、INACTIVEのいずれかを指定してください")
            private String status;

            private Date createdAt;

            private Date updatedAt;

            // ==================== ビジネスメソッド ====================

            /**
             * 新規レコードかどうかを判定
             * @return IDがnullの場合true
             */
            public boolean isNewRecord () {
                return id == null;
            }

            /**
             * 更新対象レコードかどうかを判定
             * @return IDが存在し、正の値の場合true
             */
            public boolean isValidForUpdate () {
                return id != null && id > 0;
            }

            /**
             * 関連検証が必要かどうかを判定
             * @return 関連IDが設定されている場合true
             */
            public boolean requiresRelatedValidation () {
                return relatedId != null;
            }

            /**
             * 有効なステータスを取得
             * @return ステータスが設定されていない場合はデフォルト値"ACTIVE"を返す
             */
            public String getEffectiveStatus () {
                return status != null && !status.trim().isEmpty() ? status.toUpperCase() : "ACTIVE";
            }

            // ==================== データベース変換メソッド（パフォーマンス最適化） ====================

            /**
             * DTOをデータベースフィールドのMapに変換します（静的変換、反射なし）
             * パフォーマンス最適化のため、反射を使用せずに直接フィールドマッピングを行います
             *
             * @param isInsert 挿入操作であるかどうか
             * @return データベースフィールドのMap
             */
            public Map<String, Object> toDatabaseFields ( boolean isInsert){
                Map<String, Object> fields = new HashMap<>();

                // IDフィールド（新規レコードの場合は除外）
                if (!isNewRecord() && id != null) {
                    fields.put("id", id);
                }

                // 基本フィールド
                fields.put("field1", field1 != null ? field1.trim().toLowerCase() : null);
                fields.put("field2", field2 != null && field2.trim().isEmpty() ? null : field2);

                // 外部キーフィールド
                fields.put("related_id", relatedId != null && relatedId > 0 ? relatedId : null);

                // ステータス（業務ロジック適用）
                fields.put("status", getEffectiveStatus());

                // 自動生成フィールド
                Date currentTime = new Date();
                if (isInsert) {
                    // 挿入時のみ作成日時を設定
                    fields.put("created_at", currentTime);
                }
                // 更新日時は常に設定
                fields.put("updated_at", currentTime);

                return fields;
            }
        }
```

### 2. DTOコンバーターの使用

**重要：パフォーマンス最適化のため、DTOConverterはユーティリティクラスに変更されました。
業務実装時は継承不要で、静的メソッドを直接使用します。**

```java
// DTOConverterはユーティリティクラスとして使用

import com.ms.bp.shared.common.io.converter.DTOConverter;

// 業務コード内での使用例
public class SomeBusinessService {

    public void processData() {
        UserImportDTO dto = new UserImportDTO();
        // ... DTOにデータを設定

        // 静的メソッドを直接使用（継承不要）
        Map<String, Object> fields = DTOConverter.toDatabase(dto, true);

        // データベース操作に使用
        // ...
    }
}
```

**変更点：**
- **継承不要**：DTOConverterを継承する必要がなくなりました
- **ユーティリティクラス**：静的メソッドを直接使用します
- **コード簡素化**：不要なコンバータークラスを削除できます
- **型安全性**：泛型约束により、DatabaseMappableを実装するDTOのみを受け入れます

**削除されたもの：**
- UserImportDTOConverterなどの個別コンバータークラス
- AbstractImportServiceのgetDTOConverter()メソッド
- 継承ベースの設計

### 3. インポートサービスクラスの作成

```java
package com.ms.bp.domain.importexport.

[business];

import com.ms.bp.domain.file.base.AbstractImportService;
[business].converter.[BusinessName]ImportDTOConverter;
        [BusinessName]ImportDTO;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.converter.DTOConverter;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.io.validator.DataValidator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * [BusinessName]インポートサービスの実装クラス（DTOベース）
 */
public class [BusinessName]ImportService extends AbstractImportService<[BusinessName]ImportDTO>{

        @Override
        protected String getDataType() {
            return "[BUSINESS_NAME]";
        }

        @Override
        protected Class<[BusinessName]ImportDTO>

        getDTOClass() {
            return [BusinessName]ImportDTO.class;
        }

        @Override
        protected DTOConverter<[BusinessName]ImportDTO>

        getDTOConverter() {
            return new[BusinessName] ImportDTOConverter();
        }

        @Override
        protected DataValidator getDataValidator() {
            // DTOベースのバリデーターを使用（アノテーション検証 + カスタム検証）
            return new DTODataValidator<>([BusinessName] ImportDTO.class, this::validateCustomLogic);
        }

        @Override
        protected ImportOptions buildImportOptions() {
            return ImportOptions.builder()
                    .format(FileFormat.CSV)
                    .hasHeader(true)
                    .batchSize(100)
                    .targetTable("[table_name]")
                    .keyColumns(Arrays.asList("id")) // 主キー列
                    .upsertMode(true)
                    .skipValidation(false)
                    .continueOnError(true)
                    .build();
        }

        /**
         * カスタムビジネスロジック検証
         * アノテーション検証では対応できない複雑な検証ロジックを実装
         *
         * @param data 検証対象のデータ
         * @param options インポートオプション
         * @return 検証エラーのリスト
         */
        protected List<ValidationError> validateCustomLogic(Map<String, Object> data, ImportOptions options) {
            List<ValidationError> errors = new ArrayList<>();

            // キー列の検証（UPSERTモードまたは明示的に指定された場合）
            if (options.getKeyColumns() != null && !options.getKeyColumns().isEmpty()) {
                for (String keyColumn : options.getKeyColumns()) {
                    Object keyValue = data.get(keyColumn);
                    if (keyValue != null && keyValue.toString().trim().isEmpty()) {
                        errors.add(new ValidationError(keyColumn, keyValue, "キー列は空にできません"));
                    }
                }
            }

            // TODO: 将来的にはデータベースとの整合性チェックなどの複雑な検証をここに追加
            // 例：
            // - フィールド1の重複チェック
            // - 関連IDの存在チェック
            // - ビジネスルールに基づく検証

            return errors;
        }
}
```

### 4. インポート機能の注意点

#### A. DTO設計原則
- **ドメインオブジェクトとして**：関連するビジネスメソッドを含む
- **検証アノテーション**：アノテーションを使用した基本検証
- **インターフェース実装**：`DatabaseMappable`インターフェースを必ず実装
- **業務ロジック**：DTOに業務メソッドを実装してドメインロジックを表現

#### B. データベースフィールドマッピング
- **インターフェース実装必須**：`DatabaseMappable.toDatabaseFields()`メソッドによる直接マッピング
- **設定より規約**：camelCase → snake_caseの変換は静的メソッド内で実装
- **自動生成**：静的メソッド内で直接実装（new Date()など）
- **アノテーション不使用**：@DatabaseFieldアノテーションは使用しない

#### C. ビジネスロジックの分離
- **シンプルなロジック**：DTOのビジネスメソッドに配置
- **データ変換ロジック**：DTOの`toDatabaseFields()`メソッド内に配置
- **複雑なロジック**：DTOのビジネスメソッドまたは専用のサービスクラスに配置

#### D. 検証の階層構造
1. **アノテーション検証**：基本的な形式チェック（必須、長さ、形式など）
2. **DTO業務メソッド**：ドメイン固有の検証ロジック
3. **カスタム検証**：データベース整合性やビジネスルール検証

## 共通注意事項

### 1. 命名規則とパッケージ構成
- **サービスクラス**：`[BusinessName]ImportService`, `[BusinessName]ExportService`
- **DTOクラス**：`[BusinessName]ImportDTO`
- **コンバーター**：`[BusinessName]ImportDTOConverter`
- **パッケージ構成**：
  - サービス：`com.ms.bp.domain.importexport.[business]`
  - DTO：`com.ms.bp.model.dto`
  - コンバーター：`com.ms.bp.domain.importexport.[business].converter`

### 2. 設定のポイント
- **バッチ処理サイズ**：インポート100、エクスポート1000
- **エラー処理**：continueOnError=true（処理を継続）
- **S3パス**：業務別に分類して整理（`exports/[business]/`, `imports/[business]/`）
- **ファイル形式**：CSV形式、ヘッダー付き
- **文字エンコーディング**：UTF-8


## 実装例：UserImportService参考

現在のプロジェクトには`UserImportService`の実装例があります：

```java
// 参考：src/main/java/com/ms/bp/domain/importexport/user/UserImportService.java
// 参考：src/main/java/com/ms/bp/model/dto/UserImportDTO.java
// 参考：src/main/java/com/ms/bp/domain/importexport/user/converter/UserImportDTOConverter.java
```

## クイックチェックリスト

### エクスポート機能
- [ ] SQLフィールド順序とCSV columnsが一致
- [ ] フィールドエイリアスを使用して名前を一致させる
- [ ] formatDataメソッドで必要なデータフォーマットを実装
- [ ] 適切なバッチ処理サイズを設定（1000）
- [ ] 正しいS3パスを設定
- [ ] addConditionヘルパーメソッドを活用してWHERE句を構築
- [ ] **データ展開が必要な場合はDataExpanderを実装**
- [ ] **集約クエリでGROUP_CONCATを使用して複数値を取得**
- [ ] **展開処理のパフォーマンス制限を設定**

### インポート機能
- [ ] DTOに必要なフィールドと検証アノテーションを含む
- [ ] DTOに業務メソッドを実装（isNewRecord、getEffectiveStatusなど）
- [ ] **DTOに`DatabaseMappable`インターフェースを実装（必須）**
- [ ] **DTOに`toDatabaseFields()`メソッドを実装（必須）**
- [ ] DTOConverterユーティリティクラスを使用（継承不要）
- [ ] 正しいデータベーステーブルと主キーを設定
- [ ] DTODataValidatorでアノテーション検証とカスタム検証を組み合わせ

## パフォーマンス最適化

### 1. ユーティリティクラス化によるコード簡素化

**背景：**
従来のDTOConverter実装では、継承ベースの設計により不要な複雑性が生じていました。
実際にはDTOConverterは単純なユーティリティ機能のみを提供するため、ユーティリティクラス化を実施しました。

**最適化内容：**
- **継承不要**：DTOConverterを継承する必要がなくなりました
- **ユーティリティクラス**：静的メソッドによる直接呼び出し
- **コード簡素化**：不要なコンバータークラスを削除

**実装例：**
```java
// 1. DatabaseMappableインターフェースの定義
public interface DatabaseMappable {
    Map<String, Object> toDatabaseFields(boolean isInsert);
}

// 2. DTOでインターフェースを実装
public class UserImportDTO implements DatabaseMappable {
    @Override
    public Map<String, Object> toDatabaseFields(boolean isInsert) {
        // 直接フィールドマッピング（反射なし）
        Map<String, Object> fields = new HashMap<>();
        // ... 実装
        return fields;
    }
}

// 3. ユーティリティクラスとしてのDTOConverter
public final class DTOConverter {
    private DTOConverter() {
        throw new UnsupportedOperationException("ユーティリティクラスはインスタンス化できません");
    }

    public static <T extends DatabaseMappable> Map<String, Object> toDatabase(T dto, boolean isInsert) {
        if (dto == null) {
            throw new IllegalArgumentException("DTOがnullです");
        }
        return dto.toDatabaseFields(isInsert); // 直接呼び出し、反射なし
    }
}

// 4. 業務コードでの使用
public class SomeBusinessService {
    public void processData() {
        UserImportDTO dto = new UserImportDTO();
        // 静的メソッドを直接使用（継承不要）
        Map<String, Object> fields = DTOConverter.toDatabase(dto, true);
    }
}
```

**性能向上効果：**
- **反射完全回避**：Method.invokeも含めて一切の反射を使用しない
- **JVM最適化**：直接メソッド呼び出しによるインライン最適化
- **メモリ使用量削減**：不要なオブジェクト生成を削除
- **コード簡素化**：継承ベースの複雑性を排除

### 2. 実装推奨事項

**新規DTO実装時：**
- 必ず`DatabaseMappable`インターフェースを実装する
- `toDatabaseFields()`メソッドで業務ロジックを統合する
- DTOConverterは静的メソッドとして使用する

**既存DTO移行時：**
- すべてのDTOに`DatabaseMappable`インターフェースを追加する
- 個別のDTOConverterクラスを削除する
- AbstractImportServiceから`getDTOConverter()`メソッドを削除する
- 一括移行を推奨（段階的移行は複雑になる）

**削除されたもの：**
- UserImportDTOConverterなどの個別コンバータークラス
- AbstractImportServiceのgetDTOConverter()メソッド
- 継承ベースの設計パターン
- 反射ベースのフィールドアクセス

## データ展開機能（1行→多行変換）

### 1. 概要

データ展開機能は、データベースから取得した1行のデータを複数行に分割してエクスポートする機能です。

**使用例：**
- ユーザーの複数ロールを各行に展開
- 商品の複数カテゴリを各行に展開
- 注文の複数商品を各行に展開

**データ処理フロー：**
```
データベース(1行) → DataExpander(1→N行) → formatData(格式化) → CSV出力(N行)
```

### 2. 実装アーキテクチャ

#### A. コンポーネント構成
- **DataExpander**: データ展開ロジックのインターフェース
- **ExpansionContext**: 展開処理のコンテキスト情報
- **AbstractExportService**: 展開処理器の統合
- **ExportTemplate**: 展開処理の実行エンジン

#### B. 処理フロー
1. **データ取得**: 集約クエリで複数値を1フィールドに格納
2. **展開判定**: `needsExpansion()`で展開の必要性を判定
3. **データ展開**: `expandData()`で1行を複数行に分割
4. **格式化**: 各展開行に対して`formatData()`を適用
5. **CSV出力**: 展開後の全行をCSVに書き込み

### 3. パフォーマンス最適化

#### A. メモリ使用量制御
- **バッチ処理**: ページング単位で展開処理を実行
- **行数制限**: `maxExpandedRows`で展開行数を制限
- **早期判定**: `needsExpansion()`で不要な処理をスキップ

#### B. 処理効率化
- **集約クエリ**: `GROUP_CONCAT`で複数値を効率的に取得
- **ストリーミング**: 展開結果を即座にCSVに書き込み
- **エラー継続**: 展開エラー時も処理を継続

### 4. 設定とカスタマイズ

#### A. 展開オプション
```java
// リクエストパラメータでの制御
{
    "defaultRole": "未設定",        // デフォルト値
    "allowedRoles": ["管理者", "操作者"], // 許可ロール
    "excludedRoles": ["無効"]       // 除外ロール
}
```

#### B. コンテキスト情報
- **ユーザー情報**: 権限ベースの展開制御
- **リクエストパラメータ**: 動的な展開条件
- **エクスポート条件**: フィルタリング条件
- **出力列リスト**: 展開後のデータ構造制御

### 5. エラー処理と監視

#### A. エラー処理戦略
- **展開エラー**: 元データを保持して処理継続
- **メモリ不足**: 行数制限で保護
- **タイムアウト**: Lambda制限時間の監視

#### B. 監視とログ
- **展開統計**: 元レコード数、展開後レコード数、展開率
- **パフォーマンス**: 処理時間、メモリ使用量
- **デバッグ情報**: 詳細な展開ログ