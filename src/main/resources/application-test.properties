# ???????
# AWS??
aws.region=ap-northeast-1
aws.s3.bucket.name=teams-budget-app-files-test

# ????????
db.secret.name=test-db-secret
db.host=test-aurora-cluster.cluster-xxx.ap-northeast-1.rds.amazonaws.com
db.port=5432
db.name=testdb
db.username=test_user
db.password=test_password

# JWT??
jwt.secret.key=test-secret-key-for-testing

# ?????
email.default.from=<EMAIL>

# Azure AD??
azure.client.id=your-test-client-id
azure.tenant.id=your-test-tenant-id

# ?????????
frontend.base.url=https://test.example.com

# ????
aws.lambda.log.format=TEXT
aws.lambda.log.level=DEBUG
