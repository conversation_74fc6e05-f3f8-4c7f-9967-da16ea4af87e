package com.ms.bp.interfaces;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.interfaces.rest.controller.DataController;
import com.ms.bp.interfaces.rest.controller.FileController;
import com.ms.bp.interfaces.rest.controller.UserAuthController;
import com.ms.bp.interfaces.rest.exception.GlobalExceptionHandler;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalErrorCodeConstants;
import com.ms.bp.shared.common.config.ConfigurationInitializer;
import com.ms.bp.interfaces.middleware.AuthMiddleware;
import com.ms.bp.model.entity.UserInfo;
import com.ms.bp.shared.util.ResponseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.*;

/**
 * すべてのAPIゲートウェイリクエストを処理するメインLambdaハンドラー
 */
public class LambdaHandler implements RequestHandler<APIGatewayProxyRequestEvent, APIGatewayProxyResponseEvent> {
    private static final Logger logger = LoggerFactory.getLogger(LambdaHandler.class);

    // APIパスの定数
    private static final String API_PATH_FILES = "/api/files/";
    private static final String API_PATH_FILES_UPLOAD_URL = "/api/files/upload-url";
    private static final String API_PATH_FILES_DOWNLOAD_URL = "/api/files/download-url";
    // データ管理エンドポイント
    private static final String API_PATH_DATA_IMPORT = "/api/data/import";
    private static final String API_PATH_DATA_EXPORT = "/api/data/export";
    private static final String API_PATH_DATA_EXPORT_STATUS = "/api/data/export/status/";
    // 認証エンドポイント
    private static final String API_PATH_AUTH = "/api/auth/";
    private static final String API_PATH_AUTH_ACTIVATION_REQUEST = "/api/auth/activation/request";
    private static final String API_PATH_AUTH_ACTIVATION_COMPLETE = "/api/auth/activation/complete";
    private static final String API_PATH_AUTH_LOGIN = "/api/auth/login";
    private static final String API_PATH_AUTH_PASSWORD_RESET_REQUEST = "/api/auth/password-reset/request";
    private static final String API_PATH_AUTH_PASSWORD_RESET_COMPLETE = "/api/auth/password-reset/complete";
    private static final String API_PATH_AUTH_USER_INFO = "/api/auth/user/";
    private static final String API_PATH_AUTH_HEALTH = "/api/auth/health";
    // HTTPメソッド定数
    private static final String HTTP_GET = "GET";
    private static final String HTTP_POST = "POST";
    private static final String HTTP_OPTIONS = "OPTIONS";

    // CORSヘッダー定数
    private static final String HEADER_ORIGIN = "Access-Control-Allow-Origin";
    private static final String HEADER_METHODS = "Access-Control-Allow-Methods";
    private static final String HEADER_HEADERS = "Access-Control-Allow-Headers";
    private static final String HEADER_CREDENTIALS = "Access-Control-Allow-Credentials";
    private static final String HEADER_AUTH = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    private final AuthMiddleware authMiddleware;
    private final FileController fileController;
    private final DataController dataController;
    private final UserAuthController userAuthController;
    /**
     * コントローラーとサービスを初期化するコンストラクタ
     */
    public LambdaHandler() {
        // 設定を初期化
        ConfigurationInitializer.initialize();

        // コントローラーとサービスの初期化
        this.authMiddleware = new AuthMiddleware();
        this.fileController = new FileController();
        this.dataController = new DataController();
        this.userAuthController = new UserAuthController();

        // デバッグログ記録
        logger.info("""
            Lambda関数が初期化されました
            現在の作業ディレクトリ: {}
            """,
                System.getProperty("user.dir")
        );
    }

    /**
     * リクエストを処理するメインハンドラーメソッド
     * @param request APIゲートウェイリクエスト
     * @param context Lambda実行コンテキスト
     * @return APIゲートウェイレスポンス
     */
    @Override
    public APIGatewayProxyResponseEvent handleRequest(APIGatewayProxyRequestEvent request, Context context) {
        // トレーシング用のリクエストIDをセット
        if (context != null) {
            MDC.put("awsRequestId", context.getAwsRequestId());
        } else {
            MDC.put("awsRequestId", "unknown");
        }

        try {
            var path = request.getPath();
            var method = request.getHttpMethod();

            logger.info("リクエスト処理: {} {}", method, path);

            // ステージ名を除去（/dev/や/prod/などのプレフィックス）
            if (path.matches("^/[^/]+/.*")) {
                path = path.replaceFirst("^/[^/]+/", "/");
                logger.info("正規化されたパス: {}", path);
            }

            // リクエストタイプに基づいて処理を分岐
            if (HTTP_OPTIONS.equals(method)) {
                return handleCorsResponse();
            }

            // リクエストをルーティングして共通レスポンスを取得
            CommonResult<?> result = routeRequest(request, path, context);

            // 共通レスポンスをAPI Gatewayレスポンスに変換
            return ResponseUtil.toApiGatewayResponse(result);

        } catch (Exception e) {
            // 未処理の例外は共通レスポンスに変換
            CommonResult<?> result = GlobalExceptionHandler.handleException(e);
            return ResponseUtil.toApiGatewayResponse(result);
        } finally {
            // MDC クリーンアップ
            MDC.clear();
        }
    }

    /**
     * リクエストをエンドポイントにルーティングする
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param context Lambda実行コンテキスト
     * @return 共通レスポンス
     */
    private CommonResult<?> routeRequest(APIGatewayProxyRequestEvent request, String path, Context context) {
        try {
            // 認証不要のエンドポイントをチェック
            if (isPublicEndpoint(path)) {
                logger.debug("認証不要エンドポイントにルーティング: {}", path);
                return handleAuthEndpoint(request, path);
            }

            // 認証トークンの抽出
            var token = extractBearerToken(request);
            if (token == null) {
                logger.warn("認証トークンが見つかりません: {} {}", request.getHttpMethod(), path);
                return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.UNAUTHORIZED);
            }

            // JWTトークンの検証
            UserInfo userInfo;
            try {
                logger.debug("認証トークンの検証を開始...");
                userInfo = AuthMiddleware.validateToken(authMiddleware, token);
                // 認証成功後にユーザー情報をMDCに追加
                MDC.put("userName", userInfo.getName());
            } catch (Exception e) {
                logger.error("認証エラー: {}", e.getMessage());
                return handleAuthenticationException(e);
            }

            // ファイル関連エンドポイント
            if (path.startsWith(API_PATH_FILES) ||
                    path.startsWith("/api/upload")) {
                logger.debug("ファイルエンドポイントにルーティング: {}", path);
                return handleFileEndpoint(request, path, userInfo);
            }

            // データ管理エンドポイント
            if (path.startsWith("/api/data/")) {
                logger.debug("データ管理エンドポイントにルーティング: {}", path);
                return handleDataEndpoint(request, path, userInfo, context);
            }

            // 認証が必要な認証エンドポイント（ユーザー情報取得など）
            if (path.startsWith(API_PATH_AUTH_USER_INFO)) {
                logger.debug("認証済み認証エンドポイントにルーティング: {}", path);
                return handleAuthenticatedAuthEndpoint(request, path, userInfo);
            }

            logger.warn("一致するルートが見つかりません: {} {}", request.getHttpMethod(), path);
            return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.NOT_FOUND);

        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * 認証エラーを処理する
     * @param e 認証中に発生した例外
     * @return 共通レスポンス
     */
    private CommonResult<?> handleAuthenticationException(Exception e) {
        if (e instanceof ServiceException) {
            return GlobalExceptionHandler.handleException(e);
        } else {
            return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID);
        }
    }

    /**
     * ファイル関連エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @return 共通レスポンス
     */
    private CommonResult<?> handleFileEndpoint(APIGatewayProxyRequestEvent request, String path, UserInfo userInfo) {
        try {
            var method = request.getHttpMethod();

            // アップロードURL生成エンドポイント
            if (path.equals(API_PATH_FILES_UPLOAD_URL) && HTTP_POST.equals(method)) {
                logger.info("アップロードURL生成エンドポイント: ユーザー={}", userInfo.getName());
                return fileController.generateUploadUrl(request, userInfo);
            }

            // ダウンロードURL生成エンドポイント
            if (path.equals(API_PATH_FILES_DOWNLOAD_URL) && HTTP_POST.equals(method)) {
                logger.info("ダウンロードURL生成エンドポイント: ユーザー={}", userInfo.getName());
                return fileController.generateDownloadUrl(request, userInfo);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * データ管理エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @param context Lambda実行コンテキスト
     * @return 共通レスポンス
     */
    private CommonResult<?> handleDataEndpoint(APIGatewayProxyRequestEvent request, String path,
                                               UserInfo userInfo, Context context) {
        try {
            var method = request.getHttpMethod();

            // データインポートエンドポイント
            if (path.equals(API_PATH_DATA_IMPORT) && HTTP_POST.equals(method)) {
                logger.info("データインポートエンドポイント: ユーザー={}", userInfo.getName());
                return dataController.importData(request, userInfo, context);
            }

            // データエクスポートエンドポイント
            if (path.equals(API_PATH_DATA_EXPORT) && HTTP_POST.equals(method)) {
                logger.info("データエクスポートエンドポイント: ユーザー={}", userInfo.getName());
                return dataController.exportData(request, userInfo,context);
            }

            // エクスポートステータス確認エンドポイント
            if (path.startsWith(API_PATH_DATA_EXPORT_STATUS) && HTTP_POST.equals(method)) {
                logger.info("エクスポートステータス確認: ユーザー={}",  userInfo.getName());
                return dataController.getExportStatus(request, "jobId", userInfo);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * 認証不要のエンドポイントかどうかを判定
     * @param path リクエストパス
     * @return 認証不要の場合true
     */
    private boolean isPublicEndpoint(String path) {
        return path.equals(API_PATH_AUTH_ACTIVATION_REQUEST) ||
               path.equals(API_PATH_AUTH_ACTIVATION_COMPLETE) ||
               path.equals(API_PATH_AUTH_LOGIN) ||
               path.equals(API_PATH_AUTH_PASSWORD_RESET_REQUEST) ||
               path.equals(API_PATH_AUTH_PASSWORD_RESET_COMPLETE) ||
               path.equals(API_PATH_AUTH_HEALTH);
    }

    /**
     * 認証不要の認証エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @return 共通レスポンス
     */
    private CommonResult<?> handleAuthEndpoint(APIGatewayProxyRequestEvent request, String path) {
        try {
            var method = request.getHttpMethod();

            // アクティベーション要求
            if (path.equals(API_PATH_AUTH_ACTIVATION_REQUEST) && HTTP_POST.equals(method)) {
                logger.info("ユーザーアクティベーション要求エンドポイント");
                APIGatewayProxyResponseEvent response = userAuthController.requestActivation(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // アクティベーション完了
            if (path.equals(API_PATH_AUTH_ACTIVATION_COMPLETE) && HTTP_POST.equals(method)) {
                logger.info("アクティベーション完了エンドポイント");
                APIGatewayProxyResponseEvent response = userAuthController.completeActivation(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // ログイン
            if (path.equals(API_PATH_AUTH_LOGIN) && HTTP_POST.equals(method)) {
                logger.info("ユーザーログインエンドポイント");
                APIGatewayProxyResponseEvent response = userAuthController.login(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // パスワードリセット要求
            if (path.equals(API_PATH_AUTH_PASSWORD_RESET_REQUEST) && HTTP_POST.equals(method)) {
                logger.info("パスワードリセット要求エンドポイント");
                APIGatewayProxyResponseEvent response = userAuthController.requestPasswordReset(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // パスワードリセット完了
            if (path.equals(API_PATH_AUTH_PASSWORD_RESET_COMPLETE) && HTTP_POST.equals(method)) {
                logger.info("パスワードリセット完了エンドポイント");
                APIGatewayProxyResponseEvent response = userAuthController.completePasswordReset(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // ヘルスチェック
            if (path.equals(API_PATH_AUTH_HEALTH) && HTTP_GET.equals(method)) {
                logger.info("認証サービスヘルスチェックエンドポイント");
                APIGatewayProxyResponseEvent response = userAuthController.healthCheck(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * 認証が必要な認証エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @return 共通レスポンス
     */
    private CommonResult<?> handleAuthenticatedAuthEndpoint(APIGatewayProxyRequestEvent request, String path, UserInfo userInfo) {
        try {
            var method = request.getHttpMethod();

            // ユーザー情報取得
            if (path.startsWith(API_PATH_AUTH_USER_INFO) && HTTP_GET.equals(method)) {
                logger.info("ユーザー情報取得エンドポイント: ユーザー={}", userInfo.getName());
                APIGatewayProxyResponseEvent response = userAuthController.getUserInfo(request);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalErrorCodeConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * APIGatewayProxyResponseEventをCommonResultに変換する
     * @param response APIGatewayレスポンス
     * @return 共通レスポンス
     */
    private CommonResult<?> convertApiGatewayResponseToCommonResult(APIGatewayProxyResponseEvent response) {
        try {
            if (response.getStatusCode() == 200) {
                // レスポンスボディをCommonResultとしてパース
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(response.getBody(), CommonResult.class);
            } else {
                // エラーレスポンスの場合
                return CommonResult.error(response.getStatusCode(), "リクエスト処理に失敗しました");
            }
        } catch (Exception e) {
            logger.error("APIGatewayレスポンスの変換に失敗しました", e);
            return CommonResult.error(GlobalErrorCodeConstants.SYSTEM_ERROR.getCode(), "レスポンス変換エラー");
        }
    }

    /**
     * CORSプリフライトリクエストのレスポンスを生成する
     * @return CORSヘッダー付きのレスポンス
     */
    private APIGatewayProxyResponseEvent handleCorsResponse() {
        var response = new APIGatewayProxyResponseEvent();
        response.setStatusCode(200);

        var headers = new HashMap<String, String>();
        headers.put(HEADER_ORIGIN, "*");
        headers.put(HEADER_METHODS, "GET, POST, PUT, DELETE, OPTIONS");
        headers.put(HEADER_HEADERS, "Content-Type, Authorization");
        headers.put(HEADER_CREDENTIALS, "true");

        response.setHeaders(headers);
        return response;
    }

    /**
     * リクエストからBearer認証トークンを抽出する
     * @param request APIゲートウェイリクエスト
     * @return 抽出されたトークン、または存在しない場合はnull
     */
    private String extractBearerToken(APIGatewayProxyRequestEvent request) {
        return Optional.ofNullable(request.getHeaders())
                .map(headers -> headers.get(HEADER_AUTH))
                .filter(auth -> auth.startsWith(BEARER_PREFIX))
                .map(auth -> auth.substring(BEARER_PREFIX.length()))
                .orElse(null);
    }
}