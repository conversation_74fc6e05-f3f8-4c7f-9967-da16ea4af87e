package com.ms.bp.interfaces.middleware;

import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.JwkProviderBuilder;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalErrorCodeConstants;
import com.ms.bp.shared.common.config.ConfigurationManager;
import com.ms.bp.model.entity.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.MalformedURLException;
import java.net.URL;
import java.security.interfaces.RSAPublicKey;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 認証ミドルウェアクラス
 * Microsoft Azure AD JWTトークンの検証を行う
 */
public class AuthMiddleware {
    private static final Logger logger = LoggerFactory.getLogger(AuthMiddleware.class);

    // Azure AD認証関連の定数
    private static final String JWK_URL = "https://login.microsoftonline.com/common/discovery/v2.0/keys";
    private static final String AUDIENCE_PREFIX = "api://din4ui1ec3zzc.cloudfront.net/";
    private static final String ISSUER_STS_FORMAT = "https://sts.windows.net/%s";
    private static final String ISSUER_LOGIN_FORMAT = "https://login.microsoftonline.com/%s";
    private static final String ISSUER_LOGIN_V2_FORMAT = "https://login.microsoftonline.com/%s/v2.0";

    // JWKSプロバイダーのキャッシュと制限の設定
    private static final int JWK_CACHE_SIZE = 10;
    private static final int JWK_CACHE_HOURS = 24;
    private static final int RATE_LIMIT_REQUESTS = 100;
    private static final int RATE_LIMIT_MINUTES = 1;

    // JWTクレーム名
    private static final String CLAIM_OID = "oid";
    private static final String CLAIM_NAME = "name";
    private static final String CLAIM_USERNAME = "preferred_username";
    private static final String CLAIM_SCOPE = "scp";
    private static final String CLAIM_UPN = "upn";

    // エラーメッセージ
    private static final String ERROR_NO_KID = "トークンにKIDが含まれていません";
    private static final String ERROR_INVALID_ISSUER = "無効な発行者です";
    private static final String ERROR_INVALID_AUDIENCE = "無効なオーディエンスです";
    private static final String ERROR_TOKEN_EXPIRED = "The Token has expired";
    private static final String ERROR_INVALID_ISSUER_MSG = "The Issuer is invalid";
    private static final String ERROR_INVALID_AUDIENCE_MSG = "The Audience is invalid";

    private JwkProvider jwkProvider;
    private final String clientId;
    private final String tenantId;
    private final List<String> possibleIssuers;
    private final ConfigurationManager config;

    /**
     * 認証ミドルウェアの初期化
     */
    public AuthMiddleware() {
        // 設定管理器を初期化
        this.config = ConfigurationManager.getInstance();

        // JWKプロバイダーの初期化（キャッシュとレート制限を適用）
        try {
            URL url  = new URL(JWK_URL);
            this.jwkProvider = new JwkProviderBuilder(url)
                    .cached(JWK_CACHE_SIZE, JWK_CACHE_HOURS, TimeUnit.HOURS)
                    .rateLimited(RATE_LIMIT_REQUESTS, RATE_LIMIT_MINUTES, TimeUnit.MINUTES)
                    .build();
        } catch (MalformedURLException e) {
            logger.error("JWK URLが無効です");
        }

        // 設定ファイルから設定を取得
        this.clientId = config.getProperty("azure.client.id");
        this.tenantId = config.getProperty("azure.tenant.id");

        // 可能な発行者のリストを初期化
        this.possibleIssuers = Optional.ofNullable(tenantId)
                .map(id -> List.of(
                        ISSUER_STS_FORMAT.formatted(id),
                        ISSUER_LOGIN_V2_FORMAT.formatted(id),
                        ISSUER_LOGIN_FORMAT.formatted(id)
                ))
                .orElse(List.of());

        logger.info("AuthMiddlewareが初期化されました。clientId: {}, tenantId: {}",
                Optional.ofNullable(clientId).orElse("未設定"),
                Optional.ofNullable(tenantId).orElse("未設定"));
    }

    /**
     * JWTトークンを検証する
     * @param token 検証するJWTトークン
     * @return 検証済みユーザー情報
     * @throws Exception 検証エラー
     */
    public static UserInfo validateToken(AuthMiddleware authMiddleware, String token) throws Exception {

        // トークン入力検証
        if (token == null || token.isBlank()) {
            logger.error("トークンがnullまたは空です");
            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID);
        }

        try {
            // トークンをデコード（署名検証なし）してkidを取得
            DecodedJWT jwt = JWT.decode(token);

            // KIDチェック - Pattern Matching
            String kid = switch (jwt.getKeyId()) {
                case null -> throw new ServiceException(
                        GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(), ERROR_NO_KID);
                case String s -> s;
            };

            // 署名キーを取得
            RSAPublicKey publicKey = (RSAPublicKey) authMiddleware.jwkProvider.get(kid).getPublicKey();

            // トークンから発行者とAudienceを取得
            String actualIssuer = jwt.getIssuer();
            String expectedAudience = AUDIENCE_PREFIX + authMiddleware.clientId;

            // 発行者の検証 - 条件演算子
            if (authMiddleware.possibleIssuers.contains(actualIssuer)) {
                logger.debug("発行者は有効です: {}", actualIssuer);
            } else {
                logger.warn("""
                            未サポートの発行者: {}
                            サポートされている発行者: {}
                            """, actualIssuer, authMiddleware.possibleIssuers);
            }

            // トークン検証
            Algorithm algorithm = Algorithm.RSA256(publicKey, null);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(actualIssuer)
                    .withAudience(expectedAudience)
                    .build();

            logger.info("トークン検証オプション: issuer={}, audience={}", actualIssuer, expectedAudience);

            // トークンを検証してユーザー情報を作成
            DecodedJWT verifiedJwt = verifier.verify(token);
            return authMiddleware.createUserInfo(verifiedJwt);

        } catch (JWTVerificationException e) {
            logger.error("トークン検証エラー: {}", e.getMessage());

            // エラーメッセージのパターンマッチングによる例外マッピング
            String message = e.getMessage();
            if (message != null) {
                if (message.contains(ERROR_TOKEN_EXPIRED)) {
                    throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_EXPIRED);
                } else if (message.contains(ERROR_INVALID_ISSUER_MSG)) {
                    throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                            ERROR_INVALID_ISSUER);
                } else if (message.contains(ERROR_INVALID_AUDIENCE_MSG)) {
                    throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                            ERROR_INVALID_AUDIENCE);
                }
            }

            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID);
        } catch (Exception e) {
            logger.error("トークン検証中に予期しないエラーが発生しました", e);
            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID);
        }
    }

    /**
     * 検証済みJWTからユーザー情報を作成する
     * @param jwt 検証済みJWT
     * @return ユーザー情報
     */
    private UserInfo createUserInfo(DecodedJWT jwt) {
        var userInfo = new UserInfo();

        // OIDの設定
        userInfo.setOid(Optional.ofNullable(jwt.getClaim(CLAIM_OID).asString())
                .orElse(jwt.getSubject()));

        // 名前の設定
        userInfo.setName(jwt.getClaim(CLAIM_NAME).asString());
        userInfo.setPreferredUsername(jwt.getClaim(CLAIM_USERNAME).asString());

        // メールアドレス
        userInfo.setMail(jwt.getClaim(CLAIM_UPN).asString());

        logger.info("認証成功: userId={}, userName={}",
                userInfo.getOid(), userInfo.getName());

        return userInfo;
    }

}