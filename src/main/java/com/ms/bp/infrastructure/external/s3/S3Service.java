package com.ms.bp.infrastructure.external.s3;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.shared.common.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.*;

import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * S3ストレージサービスクラス
 * ファイルのアップロード、ダウンロード、メタデータ管理を行う
 */
public class S3Service implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(S3Service.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Pattern FILE_ID_PATTERN = Pattern.compile("([^/]+)(?:\\.[^.]+)?$");
    private static final Pattern FILE_NAME_PATTERN = Pattern.compile("([^/]+)$");

    // MIME type mappings
    private static final Map<String, String> MIME_TYPES = Map.of(
            "csv", "text/csv"
    );

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;
    private final String bucketName;
    private final ConfigurationManager config;

    /**
     * S3サービスを初期化
     */
    public S3Service() {
        // 設定管理器を初期化
        this.config = ConfigurationManager.getInstance();

        // 設定ファイルからリージョンを取得（デフォルトは東京リージョン）
        String awsRegion = config.getProperty("aws.region", "ap-northeast-1");
        Region region = Region.of(awsRegion);

        // TODO: 本番環境では適切な認証方式を使用する
        String accessKeyId = "********************";
        String secretAccessKey = "jvUf2R2jhdo1zPMhBlhvwd7vWycQfMra2qmxbrqq";
        AwsCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
                AwsBasicCredentials.create(accessKeyId, secretAccessKey));

        // S3クライアントを初期化
        this.s3Client = S3Client.builder()
                .region(region)
                .credentialsProvider(credentialsProvider)
                .build();

        // S3署名者を初期化
        this.s3Presigner = S3Presigner.builder()
                .region(region)
                .build();

        // バケット名を設定ファイルから取得
        this.bucketName = config.getProperty("aws.s3.bucket.name", "teams-budget-app-files");

        logger.info("S3サービスが初期化されました。リージョン: {}, バケット: {}", awsRegion, bucketName);
    }

    @Override
    public void close() {
        s3Presigner.close();
        s3Client.close();
    }

    /**
     * S3のURL（s3://bucket-name/key形式）からInputStreamを取得
     * @param key S3のkey
     * @return 対象オブジェクトのInputStream
     * @throws IOException 取得エラー
     */
    public ResponseInputStream<GetObjectResponse> getInputStreamFromS3Url(String key) throws IOException {
        try {

            logger.info("S3オブジェクトのInputStreamを取得: バケット={}, キー={}", bucketName, key);

            // S3からオブジェクトを取得
            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(this.bucketName)
                    .key(key)
                    .build();

            return s3Client.getObject(request);

        } catch (Exception e) {
            logger.error("S3からのInputStream取得エラー: {}", e.getMessage(), e);
            throw new IOException("S3からのInputStream取得に失敗しました: " + e.getMessage(), e);
        }
    }

    /**
     * ストリームからS3にファイルをアップロード
     * @param inputStream 入力ストリーム
     * @param key S3のキー（パス）
     * @param contentType コンテンツタイプ
     * @param metadata メタデータ
     * @return アップロード結果
     * @throws IOException アップロードエラー
     */
    public Map<String, Object> uploadFileFromStream(InputStream inputStream, String key, String contentType, Long fileSize, Map<String, Object> metadata) throws IOException {
        try {
            // メタデータをBase64エンコード
            Map<String, String> encodedMetadata = encodeMetadata(metadata);

            // PutObjectRequestを作成
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .metadata(encodedMetadata)
                    .build();

            // RequestBodyをストリームから作成
            RequestBody requestBody = RequestBody.fromInputStream(inputStream, fileSize); // -1はサイズ不明を示す

            // ファイルをアップロード
            PutObjectResponse response = s3Client.putObject(
                    request,
                    requestBody
            );

            // 成功レスポンスを返す
            return Map.of(
                    "success", true,
                    "key", key,
                    "eTag", response.eTag()
            );
        } catch (Exception e) {
            logger.error("S3ストリームアップロードエラー: {}", e.getMessage(), e);
            throw new IOException("S3へのストリームアップロードに失敗しました: " + e.getMessage(), e);
        }
    }

    /**
     * 署名付きアップロードURLを生成
     * デフォルトはExcelファイルで有効期限は5分
     * S3パスが指定されていない場合はデフォルトパスを生成
     * @return 署名付きアップロードURL
     * @throws IOException 生成エラー
     */
    public String getSignedUploadUrl() throws IOException {
        // デフォルトのS3パスを生成
        String key = generateDefaultS3Path();
        return getSignedUploadUrl(key, null);
    }

    /**
     * 署名付きアップロードURLを生成
     * デフォルトはExcelファイル用で有効期限は5分
     * @param key S3のキー（パス）
     * @return 署名付きアップロードURL
     * @throws IOException 生成エラー
     */
    public String getSignedUploadUrl(String key) throws IOException {
        return getSignedUploadUrl(key, null);
    }

    /**
     * 署名付きアップロードURLを生成
     * デフォルトはExcelファイル用で有効期限は5分
     * @param key S3のキー（パス）
     * @param metadata メタデータ (オプション)
     * @return 署名付きアップロードURL
     * @throws IOException 生成エラー
     */
    public String getSignedUploadUrl(String key, Map<String, Object> metadata) throws IOException {
        try {
            // キーがnullの場合はデフォルトのS3パスを生成
            if (key == null || key.isEmpty()) {
                key = generateDefaultS3Path();
            }

            // デフォルトはExcelファイル用のMIMEタイプ
            String contentType = MIME_TYPES.get("csv");

            // 有効期限は5分(300秒)
            long expiresInSeconds = 300;

            // PutObjectRequestビルダー
            PutObjectRequest.Builder requestBuilder = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType);

            // メタデータが提供されている場合は追加
            if (metadata != null && !metadata.isEmpty()) {
                Map<String, String> encodedMetadata = encodeMetadata(metadata);
                requestBuilder.metadata(encodedMetadata);
            }

            // PutObjectRequestを構築
            PutObjectRequest putObjectRequest = requestBuilder.build();

            // 署名付きリクエストを作成
            PutObjectPresignRequest preSignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofSeconds(expiresInSeconds))
                    .putObjectRequest(putObjectRequest)
                    .build();

            // URLを生成
            PresignedPutObjectRequest preSignedRequest = s3Presigner.presignPutObject(preSignRequest);

            logger.info("署名付きアップロードURL生成成功: {}", key);
            return preSignedRequest.url().toString();
        } catch (Exception e) {
            logger.error("署名付きアップロードURL生成エラー: {}", e.getMessage(), e);
            throw new IOException("アップロードURLの生成に失敗しました: " + e.getMessage(), e);
        }
    }

    /**
     * デフォルトのS3パスを生成
     * @return 生成されたS3パス
     */
    private String generateDefaultS3Path() {
        // 現在の日付を取得
        String dateStr = java.time.LocalDate.now().toString();

        // UUIDを生成
        String fileId = UUID.randomUUID().toString();

        // デフォルトパス: uploads/temp/{日付}/{UUID}.xlsx
        return String.format("uploads/temp/%s/%s.xlsx", dateStr, fileId);
    }

    /**
     * 署名付きダウンロードURLを生成
     * @param key S3のキー（パス）
     * @param expiresInSeconds URL有効期間（秒）
     * @return 署名付きURL
     * @throws IOException 生成エラー
     */
    public String getSignedDownloadUrl(String key, long expiresInSeconds) throws IOException {
        try {
            // 署名付きリクエストを作成
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            GetObjectPresignRequest preSignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofSeconds(expiresInSeconds))
                    .getObjectRequest(getObjectRequest)
                    .build();

            // URLを生成
            PresignedGetObjectRequest preSignedRequest = s3Presigner.presignGetObject(preSignRequest);
            return preSignedRequest.url().toString();
        } catch (Exception e) {
            logger.error("署名付きURL生成エラー: {}", e.getMessage(), e);
            throw new IOException("ダウンロードURLの生成に失敗しました: " + e.getMessage(), e);
        }
    }


    /**
     * S3オブジェクトのメタデータを取得してデコード
     * @param key S3のキー（パス）
     * @return デコードされたメタデータ
     * @throws IOException 取得エラー
     */
    public Map<String, Object> getObjectMetadata(String key) throws IOException {
        try (ResponseInputStream<GetObjectResponse> response = s3Client.getObject(
                GetObjectRequest.builder()
                        .bucket(bucketName)
                        .key(key)
                        .build())) {

            return decodeMetadata(response.response().metadata());
        } catch (Exception e) {
            logger.error("メタデータ取得エラー: {}", e.getMessage(), e);
            throw new IOException("S3オブジェクトのメタデータ取得に失敗しました: " + e.getMessage(), e);
        }
    }



    // ヘルパーメソッド

    /**
     * メタデータをBase64エンコード
     * HTTPヘッダーでは非ASCII文字が使えないため、エンコードが必要
     */
    private Map<String, String> encodeMetadata(Map<String, Object> metadata) throws IOException {
        Map<String, String> encodedMetadata = new HashMap<>();

        for (var entry : metadata.entrySet()) {
            // 値をJSON文字列化してからBase64エンコード
            String jsonValue = OBJECT_MAPPER.writeValueAsString(entry.getValue());
            String encodedValue = Base64.getEncoder().encodeToString(jsonValue.getBytes());
            encodedMetadata.put(entry.getKey(), encodedValue);
        }

        return encodedMetadata;
    }

    /**
     * Base64エンコードされたメタデータをデコード
     */
    private Map<String, Object> decodeMetadata(Map<String, String> encodedMetadata) {
        return encodedMetadata.entrySet().stream()
                .map(entry -> {
                    try {
                        // Base64デコードしてからJSONパース
                        byte[] decodedBytes = Base64.getDecoder().decode(entry.getValue());
                        String jsonValue = new String(decodedBytes);
                        Object value = OBJECT_MAPPER.readValue(jsonValue, Object.class);
                        return Map.entry(entry.getKey(), value);
                    } catch (Exception e) {
                        logger.warn("メタデータのデコードに失敗: {}", entry.getKey());
                        return Map.entry(entry.getKey(), (Object)entry.getValue());
                    }
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

}