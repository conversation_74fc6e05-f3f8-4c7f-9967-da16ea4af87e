package com.ms.bp.infrastructure.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.ms.bp.model.entity.User;
import com.ms.bp.shared.common.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT トークン生成ユーティリティクラス
 * ユーザー認証後のJWTトークンを生成する
 */
public class JwtTokenGenerator {
    private static final Logger logger = LoggerFactory.getLogger(JwtTokenGenerator.class);
    private static final ConfigurationManager config = ConfigurationManager.getInstance();

    // JWT設定
    private static final String ISSUER = "ms-bp-auth";
    private static final String SECRET_KEY = config.getProperty("jwt.secret.key", "default-secret-key-for-development");
    
    // トークンの有効期限（時間）
    private static final int ACCESS_TOKEN_EXPIRY_HOURS = 8; // 8時間
    private static final int REFRESH_TOKEN_EXPIRY_DAYS = 7;  // 7日間
    
    // クレーム名
    private static final String CLAIM_USER_ID = "userId";
    private static final String CLAIM_USERNAME = "username";
    private static final String CLAIM_EMAIL = "email";
    private static final String CLAIM_ROLE_ID = "roleId";
    private static final String CLAIM_DEPARTMENT_ID = "departmentId";
    private static final String CLAIM_STATUS = "status";
    private static final String CLAIM_TOKEN_TYPE = "tokenType";
    
    /**
     * アクセストークンを生成する
     * @param user ユーザー情報
     * @return JWTアクセストークン
     */
    public static String generateAccessToken(User user) {
        if (user == null || user.getId() == null) {
            throw new IllegalArgumentException("無効なユーザー情報です");
        }
        
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
            
            Instant now = Instant.now();
            Instant expiry = now.plus(ACCESS_TOKEN_EXPIRY_HOURS, ChronoUnit.HOURS);
            
            String token = JWT.create()
                    .withIssuer(ISSUER)
                    .withSubject(user.getId().toString())
                    .withIssuedAt(Date.from(now))
                    .withExpiresAt(Date.from(expiry))
                    .withClaim(CLAIM_USER_ID, user.getId())
                    .withClaim(CLAIM_USERNAME, user.getUsername())
                    .withClaim(CLAIM_EMAIL, user.getEmail())
                    .withClaim(CLAIM_ROLE_ID, user.getRoleId())
                    .withClaim(CLAIM_DEPARTMENT_ID, user.getDepartmentId())
                    .withClaim(CLAIM_STATUS, user.getStatus())
                    .withClaim(CLAIM_TOKEN_TYPE, "ACCESS")
                    .sign(algorithm);
            
            logger.info("ユーザーID {} のアクセストークンを生成しました", user.getId());
            return token;
            
        } catch (JWTCreationException e) {
            logger.error("アクセストークンの生成に失敗しました: ユーザーID {}", user.getId(), e);
            throw new RuntimeException("アクセストークンの生成に失敗しました", e);
        }
    }
    
    /**
     * リフレッシュトークンを生成する
     * @param user ユーザー情報
     * @return JWTリフレッシュトークン
     */
    public static String generateRefreshToken(User user) {
        if (user == null || user.getId() == null) {
            throw new IllegalArgumentException("無効なユーザー情報です");
        }
        
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
            
            Instant now = Instant.now();
            Instant expiry = now.plus(REFRESH_TOKEN_EXPIRY_DAYS, ChronoUnit.DAYS);
            
            String token = JWT.create()
                    .withIssuer(ISSUER)
                    .withSubject(user.getId().toString())
                    .withIssuedAt(Date.from(now))
                    .withExpiresAt(Date.from(expiry))
                    .withClaim(CLAIM_USER_ID, user.getId())
                    .withClaim(CLAIM_USERNAME, user.getUsername())
                    .withClaim(CLAIM_TOKEN_TYPE, "REFRESH")
                    .sign(algorithm);
            
            logger.info("ユーザーID {} のリフレッシュトークンを生成しました", user.getId());
            return token;
            
        } catch (JWTCreationException e) {
            logger.error("リフレッシュトークンの生成に失敗しました: ユーザーID {}", user.getId(), e);
            throw new RuntimeException("リフレッシュトークンの生成に失敗しました", e);
        }
    }
    
    /**
     * 認証レスポンス用のトークンペアを生成する
     * @param user ユーザー情報
     * @return アクセストークンとリフレッシュトークンのマップ
     */
    public static Map<String, Object> generateTokenPair(User user) {
        String accessToken = generateAccessToken(user);
        String refreshToken = generateRefreshToken(user);
        
        Map<String, Object> tokens = new HashMap<>();
        tokens.put("accessToken", accessToken);
        tokens.put("refreshToken", refreshToken);
        tokens.put("tokenType", "Bearer");
        tokens.put("expiresIn", ACCESS_TOKEN_EXPIRY_HOURS * 3600); // 秒単位
        
        return tokens;
    }
    
    /**
     * ユーザー情報からJWTペイロードを作成する
     * @param user ユーザー情報
     * @return JWTペイロード
     */
    public static Map<String, Object> createUserPayload(User user) {
        Map<String, Object> payload = new HashMap<>();
        
        if (user != null) {
            payload.put(CLAIM_USER_ID, user.getId());
            payload.put(CLAIM_USERNAME, user.getUsername());
            payload.put(CLAIM_EMAIL, user.getEmail());
            payload.put(CLAIM_ROLE_ID, user.getRoleId());
            payload.put(CLAIM_DEPARTMENT_ID, user.getDepartmentId());
            payload.put(CLAIM_STATUS, user.getStatus());
        }
        
        return payload;
    }
}
