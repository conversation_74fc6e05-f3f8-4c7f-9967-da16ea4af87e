package com.ms.bp.domain.user;

import com.ms.bp.infrastructure.external.email.EmailService;
import com.ms.bp.infrastructure.persistence.dao.UserDAO;
import com.ms.bp.infrastructure.persistence.dao.UserTokenDAO;
import com.ms.bp.infrastructure.security.PasswordEncoder;
import com.ms.bp.infrastructure.security.TokenGenerator;
import com.ms.bp.model.entity.User;
import com.ms.bp.model.entity.UserToken;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalErrorCodeConstants;
import com.ms.bp.shared.common.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * ユーザーアクティベーション領域サービス
 * ユーザーの自己アクティベーション機能を提供する
 */
public class UserActivationService {
    private static final Logger logger = LoggerFactory.getLogger(UserActivationService.class);
    
    private final UserDAO userDAO;
    private final UserTokenDAO userTokenDAO;
    private final EmailService emailService;
    private final ConfigurationManager config;

    // アクティベーショントークンの有効期限（時間）
    private static final int ACTIVATION_TOKEN_EXPIRY_HOURS = 24;

    public UserActivationService(UserDAO userDAO, UserTokenDAO userTokenDAO, EmailService emailService) {
        this.userDAO = userDAO;
        this.userTokenDAO = userTokenDAO;
        this.emailService = emailService;
        this.config = ConfigurationManager.getInstance();
    }
    
    /**
     * ユーザー検証とアクティベーションメール送信
     * @param employeeCode 社員番号
     * @param email メールアドレス（オプション）
     * @return 処理結果メッセージ
     */
    public String initiateUserActivation(String employeeCode, String email) {
        try {
            // 入力検証
            if (employeeCode == null || employeeCode.trim().isEmpty()) {
                throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(), 
                                         "社員番号は必須です");
            }
            
            // ユーザー検索
            User user = findUserForActivation(employeeCode, email);
            
            // ユーザー状態検証
            validateUserForActivation(user);
            
            // 既存の未使用アクティベーショントークンを無効化
            userTokenDAO.invalidateUserTokens(user.getId(), "ACTIVATION");
            
            // 新しいアクティベーショントークンを生成
            String activationToken = TokenGenerator.generateActivationToken(user.getId());
            
            // トークンをデータベースに保存
            UserToken userToken = createActivationToken(user.getId(), activationToken);
            userTokenDAO.insert(userToken);
            
            // アクティベーションURLを生成
            String activationUrl = buildActivationUrl(activationToken);
            
            // アクティベーションメールを送信
            boolean emailSent = emailService.sendActivationEmail(
                user.getEmail(), 
                user.getFullName(), 
                activationUrl
            );
            
            if (!emailSent) {
                logger.error("アクティベーションメールの送信に失敗しました: userId={}", user.getId());
                throw new ServiceException(GlobalErrorCodeConstants.EXTERNAL_SERVICE_ERROR.getCode(),
                                         "メール送信に失敗しました");
            }
            
            logger.info("ユーザーアクティベーション処理を開始しました: userId={}, employeeCode={}", 
                       user.getId(), employeeCode);
            
            return "アクティベーションメールを送信しました。メールをご確認ください。";
            
        } catch (SQLException e) {
            logger.error("ユーザーアクティベーション処理中にデータベースエラーが発生しました", e);
            throw new ServiceException(GlobalErrorCodeConstants.DATABASE_ERROR.getCode(),
                                     "データベースエラーが発生しました");
        }
    }
    
    /**
     * パスワード設定とアカウントアクティベーション
     * @param token アクティベーショントークン
     * @param password 新しいパスワード
     * @return 処理結果メッセージ
     */
    public String completeUserActivation(String token, String password) {
        try {
            // 入力検証
            validateActivationInput(token, password);
            
            // トークン検証
            UserToken userToken = validateActivationToken(token);
            
            // パスワード強度チェック
            PasswordEncoder.PasswordStrength strength = PasswordEncoder.checkPasswordStrength(password);
            if (strength == PasswordEncoder.PasswordStrength.WEAK || 
                strength == PasswordEncoder.PasswordStrength.INVALID) {
                throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                         "パスワードが弱すぎます。より強力なパスワードを設定してください。");
            }
            
            // パスワードをハッシュ化
            String hashedPassword = PasswordEncoder.encode(password);
            
            // ユーザーをアクティベーション（パスワード設定 + ステータス更新）
            userDAO.activateUser(userToken.getUserId(), hashedPassword);
            
            // トークンを使用済みにマーク
            userTokenDAO.markTokenAsUsed(userToken.getId());
            
            logger.info("ユーザーアクティベーションが完了しました: userId={}", userToken.getUserId());
            
            return "アカウントのアクティベーションが完了しました。ログインできます。";
            
        } catch (SQLException e) {
            logger.error("ユーザーアクティベーション完了処理中にデータベースエラーが発生しました", e);
            throw new ServiceException(GlobalErrorCodeConstants.DATABASE_ERROR.getCode(),
                                     "データベースエラーが発生しました");
        }
    }
    
    /**
     * アクティベーション対象ユーザーを検索
     */
    private User findUserForActivation(String employeeCode, String email) throws SQLException {
        User user;
        
        if (email != null && !email.trim().isEmpty()) {
            // 社員番号とメールアドレス両方で検索
            user = userDAO.findByEmployeeCodeAndEmail(employeeCode.trim().toUpperCase(), 
                                                    email.trim().toLowerCase());
        } else {
            // 社員番号のみで検索
            user = userDAO.findByEmployeeCode(employeeCode.trim().toUpperCase());
        }
        
        if (user == null) {
            throw new ServiceException(GlobalErrorCodeConstants.USER_NOT_FOUND.getCode(),
                                     "指定された条件に一致するユーザーが見つかりません");
        }
        
        return user;
    }
    
    /**
     * アクティベーション対象ユーザーの状態を検証
     */
    private void validateUserForActivation(User user) {
        if (user.isActive() && user.hasPassword()) {
            throw new ServiceException(GlobalErrorCodeConstants.BUSINESS_ERROR.getCode(),
                                     "このユーザーは既にアクティベーション済みです");
        }
        
        if (!"INACTIVE".equals(user.getStatus())) {
            throw new ServiceException(GlobalErrorCodeConstants.BUSINESS_ERROR.getCode(),
                                     "このユーザーはアクティベーション対象ではありません");
        }
    }
    
    /**
     * アクティベーショントークンを作成
     */
    private UserToken createActivationToken(Long userId, String token) {
        UserToken userToken = new UserToken();
        userToken.setUserId(userId);
        userToken.setToken(token);
        userToken.setTokenType("ACTIVATION");
        
        // 有効期限を設定（24時間後）
        LocalDateTime expiryTime = LocalDateTime.now().plusHours(ACTIVATION_TOKEN_EXPIRY_HOURS);
        userToken.setExpiresAt(Date.from(expiryTime.atZone(ZoneId.systemDefault()).toInstant()));
        
        return userToken;
    }
    
    /**
     * アクティベーションURLを構築
     */
    private String buildActivationUrl(String token) {
        String frontendBaseUrl = config.getProperty("frontend.base.url", "https://example.com");
        return frontendBaseUrl + "/auth/activate?token=" + token;
    }
    
    /**
     * アクティベーション入力を検証
     */
    private void validateActivationInput(String token, String password) {
        if (token == null || token.trim().isEmpty()) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "アクティベーショントークンは必須です");
        }
        
        if (password == null || password.isEmpty()) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "パスワードは必須です");
        }
        
        if (password.length() < 8) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "パスワードは8文字以上で設定してください");
        }
    }
    
    /**
     * アクティベーショントークンを検証
     */
    private UserToken validateActivationToken(String token) throws SQLException {
        UserToken userToken = userTokenDAO.findByToken(token);
        
        if (userToken == null) {
            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                                     "無効なアクティベーショントークンです");
        }
        
        if (!userToken.isActivationToken()) {
            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                                     "トークンタイプが正しくありません");
        }
        
        if (!userToken.isValid()) {
            if (userToken.isExpired()) {
                throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_EXPIRED.getCode(),
                                         "アクティベーショントークンの有効期限が切れています");
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                                         "アクティベーショントークンは既に使用済みです");
            }
        }
        
        return userToken;
    }
}
