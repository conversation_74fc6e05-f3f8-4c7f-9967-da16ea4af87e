package com.ms.bp.domain.user;

import com.ms.bp.infrastructure.external.email.EmailService;
import com.ms.bp.infrastructure.persistence.dao.UserDAO;
import com.ms.bp.infrastructure.persistence.dao.UserTokenDAO;
import com.ms.bp.infrastructure.security.PasswordEncoder;
import com.ms.bp.infrastructure.security.TokenGenerator;
import com.ms.bp.model.entity.User;
import com.ms.bp.model.entity.UserToken;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalErrorCodeConstants;
import com.ms.bp.shared.common.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * パスワードリセット領域サービス
 * ユーザーのパスワードリセット機能を提供する
 */
public class PasswordResetService {
    private static final Logger logger = LoggerFactory.getLogger(PasswordResetService.class);
    
    private final UserDAO userDAO;
    private final UserTokenDAO userTokenDAO;
    private final EmailService emailService;
    private final ConfigurationManager config;

    // パスワードリセットトークンの有効期限（時間）
    private static final int RESET_TOKEN_EXPIRY_HOURS = 1;
    
    public PasswordResetService(UserDAO userDAO, UserTokenDAO userTokenDAO, EmailService emailService) {
        this.userDAO = userDAO;
        this.userTokenDAO = userTokenDAO;
        this.emailService = emailService;
        this.config = ConfigurationManager.getInstance();
    }
    
    /**
     * パスワードリセット要求の受付
     * @param employeeCode 社員コード
     * @param email 登録メールアドレス
     * @return 処理結果メッセージ
     */
    public String initiatePasswordReset(String employeeCode, String email) {
        try {
            // 入力検証
            validateResetInput(employeeCode, email);
            
            // ユーザー検索
            User user = findUserForPasswordReset(employeeCode, email);
            
            // ユーザー状態検証
            validateUserForPasswordReset(user);
            
            // 既存の未使用パスワードリセットトークンを無効化
            userTokenDAO.invalidateUserTokens(user.getId(), "PASSWORD_RESET");
            
            // 新しいパスワードリセットトークンを生成
            String resetToken = TokenGenerator.generatePasswordResetToken(user.getId());
            
            // トークンをデータベースに保存
            UserToken userToken = createPasswordResetToken(user.getId(), resetToken);
            userTokenDAO.insert(userToken);
            
            // パスワードリセットURLを生成
            String resetUrl = buildPasswordResetUrl(resetToken);
            
            // パスワードリセットメールを送信
            boolean emailSent = emailService.sendPasswordResetEmail(
                user.getEmail(), 
                user.getFullName(), 
                resetUrl
            );
            
            if (!emailSent) {
                logger.error("パスワードリセットメールの送信に失敗しました: userId={}", user.getId());
                throw new ServiceException(GlobalErrorCodeConstants.EXTERNAL_SERVICE_ERROR.getCode(),
                                         "メール送信に失敗しました");
            }
            
            logger.info("パスワードリセット処理を開始しました: userId={}, employeeCode={}", 
                       user.getId(), employeeCode);
            
            return "パスワードリセットメールを送信しました。メールをご確認ください。";
            
        } catch (SQLException e) {
            logger.error("パスワードリセット処理中にデータベースエラーが発生しました", e);
            throw new ServiceException(GlobalErrorCodeConstants.DATABASE_ERROR.getCode(),
                                     "データベースエラーが発生しました");
        }
    }
    
    /**
     * パスワード更新の実行
     * @param token パスワードリセットトークン
     * @param newPassword 新しいパスワード
     * @return 処理結果メッセージ
     */
    public String completePasswordReset(String token, String newPassword) {
        try {
            // 入力検証
            validatePasswordUpdateInput(token, newPassword);
            
            // トークン検証
            UserToken userToken = validatePasswordResetToken(token);
            
            // パスワード強度チェック
            PasswordEncoder.PasswordStrength strength = PasswordEncoder.checkPasswordStrength(newPassword);
            if (strength == PasswordEncoder.PasswordStrength.WEAK || 
                strength == PasswordEncoder.PasswordStrength.INVALID) {
                throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                         "パスワードが弱すぎます。より強力なパスワードを設定してください。");
            }
            
            // パスワードをハッシュ化
            String hashedPassword = PasswordEncoder.encode(newPassword);
            
            // ユーザーのパスワードを更新
            userDAO.updatePassword(userToken.getUserId(), hashedPassword);
            
            // トークンを使用済みにマーク
            userTokenDAO.markTokenAsUsed(userToken.getId());
            
            logger.info("パスワードリセットが完了しました: userId={}", userToken.getUserId());
            
            return "パスワードの更新が完了しました。新しいパスワードでログインしてください。";
            
        } catch (SQLException e) {
            logger.error("パスワード更新処理中にデータベースエラーが発生しました", e);
            throw new ServiceException(GlobalErrorCodeConstants.DATABASE_ERROR.getCode(),
                                     "データベースエラーが発生しました");
        }
    }
    
    /**
     * パスワードリセット対象ユーザーを検索
     */
    private User findUserForPasswordReset(String employeeCode, String email) throws SQLException {
        User user = userDAO.findByEmployeeCodeAndEmail(
            employeeCode.trim().toUpperCase(), 
            email.trim().toLowerCase()
        );
        
        if (user == null) {
            // セキュリティ上、具体的な理由は明かさない
            throw new ServiceException(GlobalErrorCodeConstants.USER_NOT_FOUND.getCode(),
                                     "指定された条件に一致するユーザーが見つかりません");
        }
        
        return user;
    }
    
    /**
     * パスワードリセット対象ユーザーの状態を検証
     */
    private void validateUserForPasswordReset(User user) {
        if (!user.isActive()) {
            throw new ServiceException(GlobalErrorCodeConstants.USER_INACTIVE.getCode(),
                                     "無効なアカウントです。管理者にお問い合わせください。");
        }
        
        if (!user.hasPassword()) {
            throw new ServiceException(GlobalErrorCodeConstants.BUSINESS_ERROR.getCode(),
                                     "パスワードが設定されていないアカウントです。アクティベーションを完了してください。");
        }
        
        if ("SUSPENDED".equals(user.getStatus())) {
            throw new ServiceException(GlobalErrorCodeConstants.USER_SUSPENDED.getCode(),
                                     "停止されたアカウントです。管理者にお問い合わせください。");
        }
    }
    
    /**
     * パスワードリセットトークンを作成
     */
    private UserToken createPasswordResetToken(Long userId, String token) {
        UserToken userToken = new UserToken();
        userToken.setUserId(userId);
        userToken.setToken(token);
        userToken.setTokenType("PASSWORD_RESET");
        
        // 有効期限を設定（1時間後）
        LocalDateTime expiryTime = LocalDateTime.now().plusHours(RESET_TOKEN_EXPIRY_HOURS);
        userToken.setExpiresAt(Date.from(expiryTime.atZone(ZoneId.systemDefault()).toInstant()));
        
        return userToken;
    }
    
    /**
     * パスワードリセットURLを構築
     */
    private String buildPasswordResetUrl(String token) {
        String frontendBaseUrl = config.getProperty("frontend.base.url", "https://example.com");
        return frontendBaseUrl + "/auth/reset-password?token=" + token;
    }
    
    /**
     * パスワードリセット入力を検証
     */
    private void validateResetInput(String employeeCode, String email) {
        if (employeeCode == null || employeeCode.trim().isEmpty()) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "社員コードは必須です");
        }
        
        if (email == null || email.trim().isEmpty()) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "メールアドレスは必須です");
        }
        
        // 簡単なメール形式チェック
        if (!email.contains("@") || !email.contains(".")) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "有効なメールアドレスを入力してください");
        }
    }
    
    /**
     * パスワード更新入力を検証
     */
    private void validatePasswordUpdateInput(String token, String password) {
        if (token == null || token.trim().isEmpty()) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "パスワードリセットトークンは必須です");
        }
        
        if (password == null || password.isEmpty()) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "新しいパスワードは必須です");
        }
        
        if (password.length() < 8) {
            throw new ServiceException(GlobalErrorCodeConstants.VALIDATION_ERROR.getCode(),
                                     "パスワードは8文字以上で設定してください");
        }
    }
    
    /**
     * パスワードリセットトークンを検証
     */
    private UserToken validatePasswordResetToken(String token) throws SQLException {
        UserToken userToken = userTokenDAO.findByToken(token);
        
        if (userToken == null) {
            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                                     "無効なパスワードリセットトークンです");
        }
        
        if (!userToken.isPasswordResetToken()) {
            throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                                     "トークンタイプが正しくありません");
        }
        
        if (!userToken.isValid()) {
            if (userToken.isExpired()) {
                throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_EXPIRED.getCode(),
                                         "パスワードリセットトークンの有効期限が切れています");
            } else {
                throw new ServiceException(GlobalErrorCodeConstants.AUTH_TOKEN_INVALID.getCode(),
                                         "パスワードリセットトークンは既に使用済みです");
            }
        }
        
        return userToken;
    }
}
