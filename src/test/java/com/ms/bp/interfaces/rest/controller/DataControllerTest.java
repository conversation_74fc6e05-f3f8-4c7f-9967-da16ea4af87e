package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.domain.file.base.AbstractExportService;
import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.model.entity.ExportJobStatus;
import com.ms.bp.model.entity.ImportJobStatus;
import com.ms.bp.model.entity.UserInfo;
import com.ms.bp.model.request.ExportRequest;
import com.ms.bp.model.request.ImportRequest;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.shared.common.exception.ServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DataControllerのテストクラス
 */
@ExtendWith(MockitoExtension.class)
class DataControllerTest {

    @Mock
    private S3Service s3Service;
    
    @Mock
    private ImportJobStatusService importJobStatusService;
    
    @Mock
    private ExportJobStatusService exportJobStatusService;
    
    @Mock
    private AbstractImportService<?> importService;
    
    @Mock
    private AbstractExportService<?> exportService;
    
    @Mock
    private APIGatewayProxyRequestEvent request;
    
    @Mock
    private Context context;
    
    @Mock
    private ResponseInputStream<GetObjectResponse> inputStream;

    private DataController dataController;
    private UserInfo userInfo;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() throws Exception {
        dataController = new DataController();
        
        // モックサービスをDataControllerに注入
        injectMockServices();
        
        // テスト用ユーザー情報
        userInfo = new UserInfo();
        userInfo.setOid("123");
        userInfo.setName("testuser");
        
        objectMapper = new ObjectMapper();
    }

    /**
     * リフレクションを使用してモックサービスを注入
     */
    private void injectMockServices() throws Exception {
        // S3Serviceを注入
        Field s3ServiceField = DataController.class.getDeclaredField("s3Service");
        s3ServiceField.setAccessible(true);
        s3ServiceField.set(dataController, s3Service);
        
        // ImportJobStatusServiceを注入
        Field importJobStatusServiceField = DataController.class.getDeclaredField("importJobStatusService");
        importJobStatusServiceField.setAccessible(true);
        importJobStatusServiceField.set(dataController, importJobStatusService);
        
        // ExportJobStatusServiceを注入
        Field exportJobStatusServiceField = DataController.class.getDeclaredField("exportJobStatusService");
        exportJobStatusServiceField.setAccessible(true);
        exportJobStatusServiceField.set(dataController, exportJobStatusService);
        
        // ImportServicesマップを注入
        Field importServicesField = DataController.class.getDeclaredField("importServices");
        importServicesField.setAccessible(true);
        Map<String, AbstractImportService<?>> importServices = new HashMap<>();
        importServices.put("USERS", importService);
        importServicesField.set(dataController, importServices);
        
        // ExportServicesマップを注入
        Field exportServicesField = DataController.class.getDeclaredField("exportServices");
        exportServicesField.setAccessible(true);
        Map<String, AbstractExportService<?>> exportServices = new HashMap<>();
        exportServices.put("USERS", exportService);
        exportServicesField.set(dataController, exportServices);
    }

    @Test
    void testExportData_Success() throws Exception {
        // Given: 正常なエクスポートリクエスト
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setDataType("USERS");
        exportRequest.setFilters(new HashMap<>());
        
        String requestBody = objectMapper.writeValueAsString(exportRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // When: エクスポート処理を実行
        CommonResult<?> result = dataController.exportData(request, userInfo, context);
        
        // Then: 成功レスポンスが返されることを確認
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        @SuppressWarnings("unchecked")
        Map<String, Object> responseData = (Map<String, Object>) result.getData();
        assertEquals("ACCEPTED", responseData.get("status"));
        assertNotNull(responseData.get("jobId"));
        assertNotNull(responseData.get("statusCheckUrl"));
        
        // ジョブ作成が呼ばれることを確認
        verify(exportJobStatusService, timeout(1000)).createJob(
            anyString(), eq("USERS"), eq("123"), eq("testuser"), any(), any(), any());
    }

    @Test
    void testExportData_InvalidDataType() throws Exception {
        // Given: 無効なデータタイプのリクエスト
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setDataType("INVALID");
        exportRequest.setFilters(new HashMap<>());
        
        String requestBody = objectMapper.writeValueAsString(exportRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // When & Then: ServiceExceptionが発生することを確認
        assertThrows(ServiceException.class, () -> {
            dataController.exportData(request, userInfo, context);
        });
    }

    @Test
    void testExportData_MissingDataType() throws Exception {
        // Given: データタイプが未指定のリクエスト
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setFilters(new HashMap<>());
        
        String requestBody = objectMapper.writeValueAsString(exportRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // When & Then: ServiceExceptionが発生することを確認
        assertThrows(ServiceException.class, () -> {
            dataController.exportData(request, userInfo, context);
        });
    }

    @Test
    void testGetExportStatus_Success() throws Exception {
        // Given: 正常なジョブステータス
        String jobId = "test_job_123";
        ExportJobStatus jobStatus = new ExportJobStatus();
        jobStatus.setJobId(jobId);
        jobStatus.setStatus("COMPLETED");
        jobStatus.setMessage("エクスポート完了");
        jobStatus.setCreatedAt(new Date());
        jobStatus.setUpdatedAt(new Date());
        jobStatus.setDownloadUrl("https://example.com/download");
        jobStatus.setOutputFileName("export.zip");
        jobStatus.setFileSize(1024L);
        jobStatus.setCompletedAt(new Date());
        
        when(exportJobStatusService.getJobStatus(jobId)).thenReturn(jobStatus);
        
        // When: ステータス確認を実行
        CommonResult<?> result = dataController.getExportStatus(request, jobId, userInfo);
        
        // Then: 成功レスポンスが返されることを確認
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        verify(exportJobStatusService).getJobStatus(jobId);
    }

    @Test
    void testGetExportStatus_JobNotFound() throws Exception {
        // Given: 存在しないジョブID
        String jobId = "nonexistent_job";
        when(exportJobStatusService.getJobStatus(jobId)).thenReturn(null);
        
        // When & Then: ServiceExceptionが発生することを確認
        assertThrows(ServiceException.class, () -> {
            dataController.getExportStatus(request, jobId, userInfo);
        });
    }

    @Test
    void testImportData_Success() throws Exception {
        // Given: 正常なインポートリクエスト
        ImportRequest importRequest = new ImportRequest();
        importRequest.setDataType("USERS");
        importRequest.setS3Key("test/import.csv");
        
        String requestBody = objectMapper.writeValueAsString(importRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // When: インポート処理を実行
        CommonResult<?> result = dataController.importData(request, userInfo, context);
        
        // Then: 成功レスポンスが返されることを確認
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        @SuppressWarnings("unchecked")
        Map<String, Object> responseData = (Map<String, Object>) result.getData();
        assertEquals("ACCEPTED", responseData.get("status"));
        assertNotNull(responseData.get("jobId"));
        assertNotNull(responseData.get("statusCheckUrl"));
        
        // ジョブ作成が呼ばれることを確認
        verify(importJobStatusService, timeout(1000)).createJob(
            anyString(), eq("USERS"), eq("123"), eq("testuser"), eq("test/import.csv"), any());
    }

    @Test
    void testImportData_MissingS3Key() throws Exception {
        // Given: S3キーが未指定のリクエスト
        ImportRequest importRequest = new ImportRequest();
        importRequest.setDataType("USERS");
        
        String requestBody = objectMapper.writeValueAsString(importRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // When & Then: ServiceExceptionが発生することを確認
        assertThrows(ServiceException.class, () -> {
            dataController.importData(request, userInfo, context);
        });
    }

    @Test
    void testImportData_InvalidDataType() throws Exception {
        // Given: 無効なデータタイプのリクエスト
        ImportRequest importRequest = new ImportRequest();
        importRequest.setDataType("INVALID");
        importRequest.setS3Key("test/import.csv");
        
        String requestBody = objectMapper.writeValueAsString(importRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // When & Then: ServiceExceptionが発生することを確認
        assertThrows(ServiceException.class, () -> {
            dataController.importData(request, userInfo, context);
        });
    }

    @Test
    void testGetImportStatus_Success() throws Exception {
        // Given: 正常なジョブステータス
        String jobId = "import_job_123";
        ImportJobStatus jobStatus = new ImportJobStatus();
        jobStatus.setJobId(jobId);
        jobStatus.setStatus("COMPLETED");
        jobStatus.setMessage("インポート完了");
        jobStatus.setCreatedAt(new Date());
        jobStatus.setUpdatedAt(new Date());
        jobStatus.setInsertedCount(10);
        jobStatus.setUpdatedCount(5);
        jobStatus.setFailedCount(2);
        jobStatus.setTotalCount(17);
        jobStatus.setProcessedCount(17);
        jobStatus.setCompletedAt(new Date());
        jobStatus.setDataType("USERS");
        jobStatus.setS3Key("test/import.csv");
        jobStatus.setExecutionTimeMs(5000L);
        
        when(importJobStatusService.getJobStatus(jobId)).thenReturn(jobStatus);
        
        // When: ステータス確認を実行
        CommonResult<?> result = dataController.getImportStatus(request, jobId, userInfo);
        
        // Then: 成功レスポンスが返されることを確認
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        verify(importJobStatusService).getJobStatus(jobId);
    }

    @Test
    void testGetImportStatus_JobNotFound() throws Exception {
        // Given: 存在しないジョブID
        String jobId = "nonexistent_import_job";
        when(importJobStatusService.getJobStatus(jobId)).thenReturn(null);
        
        // When & Then: ServiceExceptionが発生することを確認
        assertThrows(ServiceException.class, () -> {
            dataController.getImportStatus(request, jobId, userInfo);
        });
    }
}
