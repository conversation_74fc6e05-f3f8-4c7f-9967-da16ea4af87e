package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.application.UserAuthApplicationService;
import com.ms.bp.model.request.*;
import com.ms.bp.model.response.UserAuthResponse;
import com.ms.bp.shared.common.CommonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserAuthControllerのテストクラス
 */
@ExtendWith(MockitoExtension.class)
class UserAuthControllerTest {

    @Mock
    private UserAuthApplicationService userAuthService;
    
    @Mock
    private APIGatewayProxyRequestEvent request;

    private UserAuthController userAuthController;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() throws Exception {
        userAuthController = new UserAuthController();
        objectMapper = new ObjectMapper();
        
        // モックサービスをコントローラーに注入
        injectMockService();
    }

    /**
     * リフレクションを使用してモックサービスを注入
     */
    private void injectMockService() throws Exception {
        Field serviceField = UserAuthController.class.getDeclaredField("userAuthService");
        serviceField.setAccessible(true);
        serviceField.set(userAuthController, userAuthService);
    }

    @Test
    void testRequestActivation_Success() throws Exception {
        // Given: 正常なアクティベーション要求
        UserActivationRequest activationRequest = new UserActivationRequest();
        activationRequest.setEmployeeCode("EMP001");
        activationRequest.setEmail("<EMAIL>");
        
        String requestBody = objectMapper.writeValueAsString(activationRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        String expectedMessage = "アクティベーションメールを送信しました。メールをご確認ください。";
        when(userAuthService.requestUserActivation(any(UserActivationRequest.class)))
            .thenReturn(expectedMessage);
        
        // When: アクティベーション要求を実行
        APIGatewayProxyResponseEvent response = userAuthController.requestActivation(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        // レスポンスボディをパース
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertEquals(expectedMessage, result.getData());
        
        verify(userAuthService).requestUserActivation(any(UserActivationRequest.class));
    }

    @Test
    void testRequestActivation_InvalidRequest() throws Exception {
        // Given: 無効なリクエストボディ
        when(request.getBody()).thenReturn("invalid json");
        
        // When: アクティベーション要求を実行
        APIGatewayProxyResponseEvent response = userAuthController.requestActivation(request);
        
        // Then: エラーレスポンスが返されることを確認
        assertEquals(200, response.getStatusCode()); // CommonResultでラップされるため200
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertFalse(result.isSuccess());
        assertEquals(50001, result.getCode()); // SYSTEM_ERROR
    }

    @Test
    void testCompleteActivation_Success() throws Exception {
        // Given: 正常なパスワード設定要求
        SetPasswordRequest passwordRequest = new SetPasswordRequest();
        passwordRequest.setToken("activation_token_123");
        passwordRequest.setPassword("NewPassword123!");
        passwordRequest.setConfirmPassword("NewPassword123!");
        
        String requestBody = objectMapper.writeValueAsString(passwordRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        String expectedMessage = "アカウントのアクティベーションが完了しました。ログインできます。";
        when(userAuthService.completeUserActivation(any(SetPasswordRequest.class)))
            .thenReturn(expectedMessage);
        
        // When: アクティベーション完了を実行
        APIGatewayProxyResponseEvent response = userAuthController.completeActivation(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertEquals(expectedMessage, result.getData());
        
        verify(userAuthService).completeUserActivation(any(SetPasswordRequest.class));
    }

    @Test
    void testLogin_Success() throws Exception {
        // Given: 正常なログイン要求
        UserLoginRequest loginRequest = new UserLoginRequest();
        loginRequest.setEmployeeCode("EMP001");
        loginRequest.setPassword("Password123!");
        loginRequest.setRememberMe(false);
        
        String requestBody = objectMapper.writeValueAsString(loginRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        // モックレスポンスを作成
        UserAuthResponse authResponse = UserAuthResponse.builder()
            .userId(1L)
            .username("testuser")
            .email("<EMAIL>")
            .accessToken("access_token_123")
            .refreshToken("refresh_token_123")
            .tokenType("Bearer")
            .expiresIn(28800)
            .build();
        
        when(userAuthService.authenticateUser(any(UserLoginRequest.class)))
            .thenReturn(authResponse);
        
        // When: ログインを実行
        APIGatewayProxyResponseEvent response = userAuthController.login(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        verify(userAuthService).authenticateUser(any(UserLoginRequest.class));
    }

    @Test
    void testLogin_AuthenticationFailure() throws Exception {
        // Given: 認証失敗のケース
        UserLoginRequest loginRequest = new UserLoginRequest();
        loginRequest.setEmployeeCode("EMP001");
        loginRequest.setPassword("WrongPassword");
        
        String requestBody = objectMapper.writeValueAsString(loginRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        when(userAuthService.authenticateUser(any(UserLoginRequest.class)))
            .thenThrow(new RuntimeException("認証に失敗しました"));
        
        // When: ログインを実行
        APIGatewayProxyResponseEvent response = userAuthController.login(request);
        
        // Then: エラーレスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertFalse(result.isSuccess());
        assertEquals(50001, result.getCode());
    }

    @Test
    void testRequestPasswordReset_Success() throws Exception {
        // Given: 正常なパスワードリセット要求
        PasswordResetRequest resetRequest = new PasswordResetRequest();
        resetRequest.setEmployeeCode("EMP001");
        resetRequest.setEmail("<EMAIL>");
        
        String requestBody = objectMapper.writeValueAsString(resetRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        String expectedMessage = "パスワードリセットメールを送信しました。メールをご確認ください。";
        when(userAuthService.requestPasswordReset(any(PasswordResetRequest.class)))
            .thenReturn(expectedMessage);
        
        // When: パスワードリセット要求を実行
        APIGatewayProxyResponseEvent response = userAuthController.requestPasswordReset(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertEquals(expectedMessage, result.getData());
        
        verify(userAuthService).requestPasswordReset(any(PasswordResetRequest.class));
    }

    @Test
    void testCompletePasswordReset_Success() throws Exception {
        // Given: 正常なパスワード更新要求
        SetPasswordRequest passwordRequest = new SetPasswordRequest();
        passwordRequest.setToken("reset_token_123");
        passwordRequest.setPassword("NewPassword123!");
        passwordRequest.setConfirmPassword("NewPassword123!");
        
        String requestBody = objectMapper.writeValueAsString(passwordRequest);
        when(request.getBody()).thenReturn(requestBody);
        
        String expectedMessage = "パスワードの更新が完了しました。新しいパスワードでログインしてください。";
        when(userAuthService.completePasswordReset(any(SetPasswordRequest.class)))
            .thenReturn(expectedMessage);
        
        // When: パスワード更新を実行
        APIGatewayProxyResponseEvent response = userAuthController.completePasswordReset(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertEquals(expectedMessage, result.getData());
        
        verify(userAuthService).completePasswordReset(any(SetPasswordRequest.class));
    }

    @Test
    void testGetUserInfo_Success() throws Exception {
        // Given: 正常なユーザー情報取得要求
        Map<String, String> pathParameters = new HashMap<>();
        pathParameters.put("userId", "123");
        when(request.getPathParameters()).thenReturn(pathParameters);
        
        UserAuthResponse userInfo = UserAuthResponse.builder()
            .userId(123L)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Test User")
            .status("ACTIVE")
            .build();
        
        when(userAuthService.getUserInfo(123L)).thenReturn(userInfo);
        
        // When: ユーザー情報取得を実行
        APIGatewayProxyResponseEvent response = userAuthController.getUserInfo(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        verify(userAuthService).getUserInfo(123L);
    }

    @Test
    void testGetUserInfo_InvalidUserId() throws Exception {
        // Given: 無効なユーザーID
        Map<String, String> pathParameters = new HashMap<>();
        pathParameters.put("userId", "invalid");
        when(request.getPathParameters()).thenReturn(pathParameters);
        
        // When: ユーザー情報取得を実行
        APIGatewayProxyResponseEvent response = userAuthController.getUserInfo(request);
        
        // Then: エラーレスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertFalse(result.isSuccess());
        assertEquals(50004, result.getCode()); // VALIDATION_ERROR
    }

    @Test
    void testHealthCheck_Success() throws Exception {
        // When: ヘルスチェックを実行
        APIGatewayProxyResponseEvent response = userAuthController.healthCheck(request);
        
        // Then: 成功レスポンスが返されることを確認
        assertEquals(200, response.getStatusCode());
        assertNotNull(response.getBody());
        
        CommonResult<?> result = objectMapper.readValue(response.getBody(), CommonResult.class);
        assertTrue(result.isSuccess());
        assertEquals("認証サービスは正常に動作しています", result.getData());
    }
}
