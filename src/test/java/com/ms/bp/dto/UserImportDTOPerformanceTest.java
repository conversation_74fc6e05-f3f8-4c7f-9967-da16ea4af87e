package com.ms.bp.dto;

import com.ms.bp.shared.common.io.converter.DTOConverter;
import com.ms.bp.model.dto.UserImportDTO;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserImportDTO パフォーマンス最適化テストクラス
 * インターフェース駆動による反射完全回避の効果を検証します
 */
public class UserImportDTOPerformanceTest {

    @Test
    void testStaticConversionMethod_basicFunctionality() {
        // Given: UserImportDTOインスタンス
        UserImportDTO dto = new UserImportDTO();
        dto.setUsername("testuser");
        dto.setEmail("  <EMAIL>  ");
        dto.setFullName("Test User");
        dto.setStatus("active");
        dto.setArea("Tokyo");
        dto.setPhoneNumber("************");

        // When: 静的変換メソッドを直接呼び出し
        Map<String, Object> fields = dto.toDatabaseFields(true);

        // Then: 基本的なフィールドマッピングが正しく動作することを確認
        assertEquals("testuser", fields.get("username"));
        assertEquals("<EMAIL>", fields.get("email")); // 正規化される
        assertEquals("Test User", fields.get("full_name"));
        assertEquals("ACTIVE", fields.get("status")); // 正規化される
        assertEquals("Tokyo", fields.get("area"));
        assertEquals("************", fields.get("phone_number"));

        // 自動生成フィールドの確認
        assertNotNull(fields.get("created_at"));
        assertNotNull(fields.get("updated_at"));
        assertTrue(fields.get("created_at") instanceof Date);
        assertTrue(fields.get("updated_at") instanceof Date);
    }

    @Test
    void testStaticConversionMethod_vs_DTOConverter() {
        // Given: UserImportDTOインスタンス
        UserImportDTO dto = new UserImportDTO();
        dto.setUsername("testuser");
        dto.setEmail("  <EMAIL>  ");
        dto.setFullName("Test User");
        dto.setStatus("active");

        // When: 静的変換メソッドを直接呼び出し
        Map<String, Object> staticFields = dto.toDatabaseFields(true);

        // When: DTOConverterユーティリティクラスを使用
        Map<String, Object> converterFields = DTOConverter.toDatabase(dto, true);

        // Then: 両方の結果が同じであることを確認
        assertEquals(staticFields.get("username"), converterFields.get("username"));
        assertEquals(staticFields.get("email"), converterFields.get("email"));
        assertEquals(staticFields.get("full_name"), converterFields.get("full_name"));
        assertEquals(staticFields.get("status"), converterFields.get("status"));

        // Then: 静的変換メソッドが使用されていることを確認
        assertEquals("<EMAIL>", staticFields.get("email"));
        assertEquals("ACTIVE", staticFields.get("status"));
    }

    @Test
    void testStaticConversionMethod_insertVsUpdate() {
        // Given: 既存ユーザーのDTO
        UserImportDTO dto = new UserImportDTO();
        dto.setId(123L);
        dto.setUsername("existinguser");
        dto.setEmail("<EMAIL>");

        // When: 挿入用変換
        Map<String, Object> insertFields = dto.toDatabaseFields(true);

        // When: 更新用変換
        Map<String, Object> updateFields = dto.toDatabaseFields(false);

        // Then: 既存ユーザーの場合、IDが含まれる
        assertEquals(123L, insertFields.get("id"));
        assertEquals(123L, updateFields.get("id"));

        // Then: 挿入時のみcreated_atが含まれる
        assertNotNull(insertFields.get("created_at"));
        assertFalse(updateFields.containsKey("created_at"));

        // Then: 両方にupdated_atが含まれる
        assertNotNull(insertFields.get("updated_at"));
        assertNotNull(updateFields.get("updated_at"));
    }

    @Test
    void testStaticConversionMethod_newUserHandling() {
        // Given: 新規ユーザーのDTO（IDがnull）
        UserImportDTO dto = new UserImportDTO();
        dto.setId(null); // 新規ユーザー
        dto.setUsername("newuser");
        dto.setEmail("<EMAIL>");

        // When: 静的変換メソッドを呼び出し
        Map<String, Object> fields = dto.toDatabaseFields(true);

        // Then: 新規ユーザーの場合、IDは含まれない
        assertFalse(fields.containsKey("id"));
        assertEquals("newuser", fields.get("username"));
        assertEquals("<EMAIL>", fields.get("email"));
    }

    @Test
    void testStaticConversionMethod_emptyStringHandling() {
        // Given: 空文字列を含むDTO
        UserImportDTO dto = new UserImportDTO();
        dto.setUsername("testuser");
        dto.setEmail("<EMAIL>");
        dto.setArea(""); // 空文字列
        dto.setPhoneNumber("   "); // 空白のみ

        // When: 静的変換メソッドを呼び出し
        Map<String, Object> fields = dto.toDatabaseFields(true);

        // Then: 空文字列はnullに変換される
        assertNull(fields.get("area"));
        assertNull(fields.get("phone_number"));
    }

    @Test
    void testPerformanceComparison_directVsUtility() {
        // Given: テスト用のDTO
        UserImportDTO dto = new UserImportDTO();
        dto.setUsername("performancetest");
        dto.setEmail("<EMAIL>");
        dto.setFullName("Performance Test");
        dto.setStatus("active");

        // 直接メソッド呼び出しのパフォーマンステスト
        long directStartTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            dto.toDatabaseFields(true);
        }
        long directEndTime = System.nanoTime();
        long directDuration = directEndTime - directStartTime;

        // DTOConverterユーティリティクラスのパフォーマンステスト
        long converterStartTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            DTOConverter.toDatabase(dto, true);
        }
        long converterEndTime = System.nanoTime();
        long converterDuration = converterEndTime - converterStartTime;

        // Then: 両方とも正常に動作することを確認
        assertTrue(directDuration > 0);
        assertTrue(converterDuration > 0);

        System.out.println("直接メソッド呼び出し実行時間: " + directDuration / 1_000_000.0 + " ms");
        System.out.println("DTOConverterユーティリティ実行時間: " + converterDuration / 1_000_000.0 + " ms");

        // 注意：ユーティリティクラスも直接メソッド呼び出しなので、パフォーマンス差は最小限
        // JVMの最適化により、両方ともほぼ同等の性能が期待される
    }

    @Test
    void testDatabaseMappableInterface() {
        // Given: UserImportDTOがDatabaseMappableを実装していることを確認
        UserImportDTO dto = new UserImportDTO();
        dto.setUsername("interfacetest");
        dto.setEmail("<EMAIL>");

        // When: インターフェース型として扱う
        com.ms.bp.shared.common.io.converter.DatabaseMappable mappable = dto;
        Map<String, Object> fields = mappable.toDatabaseFields(true);

        // Then: 正常に動作することを確認
        assertNotNull(fields);
        assertEquals("interfacetest", fields.get("username"));
        assertEquals("<EMAIL>", fields.get("email"));
    }

    @Test
    void testDTOConverterUtilityClass() {
        // Given: UserImportDTOインスタンス
        UserImportDTO dto = new UserImportDTO();
        dto.setUsername("utilitytest");
        dto.setEmail("<EMAIL>");

        // When: DTOConverterユーティリティクラスを使用
        Map<String, Object> fields = DTOConverter.toDatabase(dto, true);

        // Then: 正常に動作することを確認
        assertNotNull(fields);
        assertEquals("utilitytest", fields.get("username"));
        assertEquals("<EMAIL>", fields.get("email"));
    }

    @Test
    void testDTOConverterNullCheck() {
        // When & Then: nullのDTOを渡すと例外がスローされることを確認
        assertThrows(IllegalArgumentException.class, () -> {
            DTOConverter.toDatabase(null, true);
        });
    }

    @Test
    void testBusinessLogicIntegration() {
        // Given: 業務ロジックを含むDTO
        UserImportDTO dto = new UserImportDTO();
        dto.setEmail("  <EMAIL>  ");
        dto.setStatus(null); // nullの場合はデフォルト値が使用される
        dto.setDepartmentId(5L);

        // When: 静的変換メソッドを呼び出し
        Map<String, Object> fields = dto.toDatabaseFields(true);

        // Then: 業務ロジックが正しく適用されることを確認
        assertEquals("<EMAIL>", fields.get("email")); // getNormalizedEmail()
        assertEquals("ACTIVE", fields.get("status")); // getEffectiveStatus()
        assertEquals(5L, fields.get("department_id"));

        // Then: 業務メソッドが正しく動作することを確認
        assertTrue(dto.isNewUser()); // IDがnullなので新規ユーザー
        assertTrue(dto.requiresDepartmentValidation()); // 部門IDが設定されている
    }
}
