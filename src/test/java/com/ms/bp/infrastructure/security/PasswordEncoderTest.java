package com.ms.bp.infrastructure.security;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * PasswordEncoderのテストクラス
 */
public class PasswordEncoderTest {
    
    @Test
    void testPasswordEncoding() {
        // Given: テスト用のパスワード
        String plainPassword = "TestPassword123!";
        
        // When: パスワードをエンコード
        String encodedPassword = PasswordEncoder.encode(plainPassword);
        
        // Then: エンコードされたパスワードが正しい形式であることを確認
        assertNotNull(encodedPassword);
        assertTrue(encodedPassword.contains(":"));
        assertNotEquals(plainPassword, encodedPassword);
    }
    
    @Test
    void testPasswordMatching() {
        // Given: テスト用のパスワード
        String plainPassword = "TestPassword123!";
        String encodedPassword = PasswordEncoder.encode(plainPassword);
        
        // When & Then: 正しいパスワードでマッチング
        assertTrue(PasswordEncoder.matches(plainPassword, encodedPassword));
        
        // When & Then: 間違ったパスワードでマッチング
        assertFalse(PasswordEncoder.matches("WrongPassword", encodedPassword));
    }
    
    @Test
    void testPasswordStrengthCheck() {
        // Given & When & Then: 強いパスワード
        assertEquals(PasswordEncoder.PasswordStrength.STRONG, 
                    PasswordEncoder.checkPasswordStrength("StrongPassword123!"));
        
        // Given & When & Then: 弱いパスワード
        assertEquals(PasswordEncoder.PasswordStrength.WEAK, 
                    PasswordEncoder.checkPasswordStrength("weak"));
        
        // Given & When & Then: 無効なパスワード
        assertEquals(PasswordEncoder.PasswordStrength.INVALID, 
                    PasswordEncoder.checkPasswordStrength(""));
    }
    
    @Test
    void testNullPassword() {
        // Given & When & Then: nullパスワードの処理
        assertThrows(IllegalArgumentException.class, () -> {
            PasswordEncoder.encode(null);
        });
        
        assertFalse(PasswordEncoder.matches(null, "someHash"));
        assertFalse(PasswordEncoder.matches("password", null));
    }
}
