package com.ms.bp.infrastructure.security;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TokenGeneratorのテストクラス
 */
public class TokenGeneratorTest {
    
    @Test
    void testSecureTokenGeneration() {
        // When: セキュアトークンを生成
        String token1 = TokenGenerator.generateSecureToken();
        String token2 = TokenGenerator.generateSecureToken();
        
        // Then: トークンが生成され、異なることを確認
        assertNotNull(token1);
        assertNotNull(token2);
        assertNotEquals(token1, token2);
        assertTrue(token1.length() > 0);
        assertTrue(token2.length() > 0);
    }
    
    @Test
    void testActivationTokenGeneration() {
        // Given: テスト用のユーザーID
        Long userId = 123L;
        
        // When: アクティベーショントークンを生成
        String token = TokenGenerator.generateActivationToken(userId);
        
        // Then: トークンが正しい形式であることを確認
        assertNotNull(token);
        assertTrue(token.contains("_"));
        assertTrue(token.contains(userId.toString()));
        assertFalse(token.startsWith("PWD_"));
    }
    
    @Test
    void testPasswordResetTokenGeneration() {
        // Given: テスト用のユーザーID
        Long userId = 456L;
        
        // When: パスワードリセットトークンを生成
        String token = TokenGenerator.generatePasswordResetToken(userId);
        
        // Then: トークンが正しい形式であることを確認
        assertNotNull(token);
        assertTrue(token.startsWith("PWD_"));
        assertTrue(token.contains("_"));
        assertTrue(token.contains(userId.toString()));
    }
    
    @Test
    void testExtractUserIdFromToken() {
        // Given: テスト用のユーザーID
        Long userId = 789L;
        
        // When: アクティベーショントークンからユーザーIDを抽出
        String activationToken = TokenGenerator.generateActivationToken(userId);
        Long extractedUserId1 = TokenGenerator.extractUserIdFromToken(activationToken);
        
        // When: パスワードリセットトークンからユーザーIDを抽出
        String resetToken = TokenGenerator.generatePasswordResetToken(userId);
        Long extractedUserId2 = TokenGenerator.extractUserIdFromToken(resetToken);
        
        // Then: 正しいユーザーIDが抽出されることを確認
        assertEquals(userId, extractedUserId1);
        assertEquals(userId, extractedUserId2);
    }
    
    @Test
    void testTokenFormatValidation() {
        // Given: テスト用のトークン
        Long userId = 999L;
        String activationToken = TokenGenerator.generateActivationToken(userId);
        String resetToken = TokenGenerator.generatePasswordResetToken(userId);
        
        // When & Then: アクティベーショントークンの形式検証
        assertTrue(TokenGenerator.validateTokenFormat(activationToken, TokenGenerator.TokenType.ACTIVATION));
        assertFalse(TokenGenerator.validateTokenFormat(activationToken, TokenGenerator.TokenType.PASSWORD_RESET));
        
        // When & Then: パスワードリセットトークンの形式検証
        assertTrue(TokenGenerator.validateTokenFormat(resetToken, TokenGenerator.TokenType.PASSWORD_RESET));
        assertFalse(TokenGenerator.validateTokenFormat(resetToken, TokenGenerator.TokenType.ACTIVATION));
    }
    
    @Test
    void testInvalidTokenHandling() {
        // Given & When & Then: 無効なユーザーIDでの例外処理
        assertThrows(IllegalArgumentException.class, () -> {
            TokenGenerator.generateActivationToken(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            TokenGenerator.generateActivationToken(0L);
        });
        
        // Given & When & Then: 無効なトークンからのユーザーID抽出
        assertNull(TokenGenerator.extractUserIdFromToken(null));
        assertNull(TokenGenerator.extractUserIdFromToken(""));
        assertNull(TokenGenerator.extractUserIdFromToken("invalid_token"));
    }
}
