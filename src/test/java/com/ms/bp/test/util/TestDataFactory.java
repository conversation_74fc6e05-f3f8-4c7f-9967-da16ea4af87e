package com.ms.bp.test.util;

import com.ms.bp.model.entity.*;
import com.ms.bp.model.request.*;
import com.ms.bp.model.response.*;
import com.ms.bp.shared.common.io.model.ExportResult;
import com.ms.bp.shared.common.io.model.ImportResult;

import java.nio.file.Paths;
import java.util.*;

/**
 * テストデータファクトリー
 * テスト用のモックデータを生成するユーティリティクラス
 */
public class TestDataFactory {

    /**
     * テスト用UserInfoを作成
     */
    public static UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setOid("123");
        userInfo.setName("testuser");
        return userInfo;
    }

    /**
     * テスト用ExportRequestを作成
     */
    public static ExportRequest createTestExportRequest() {
        ExportRequest request = new ExportRequest();
        request.setDataType("USERS");
        
        Map<String, Object> filters = new HashMap<>();
        filters.put("status", "ACTIVE");
        filters.put("department", "IT");
        request.setFilters(filters);
        
        return request;
    }

    /**
     * エリア指定付きExportRequestを作成
     */
    public static ExportRequest createTestExportRequestWithAreas() {
        ExportRequest request = createTestExportRequest();
        request.getFilters().put("areas", "TOKYO,OSAKA,NAGOYA");
        return request;
    }

    /**
     * テスト用ImportRequestを作成
     */
    public static ImportRequest createTestImportRequest() {
        ImportRequest request = new ImportRequest();
        request.setDataType("USERS");
        request.setS3Key("test/import/users.csv");
        return request;
    }

    /**
     * テスト用ExportJobStatusを作成
     */
    public static ExportJobStatus createTestExportJobStatus(String status) {
        ExportJobStatus jobStatus = new ExportJobStatus();
        jobStatus.setJobId("export_job_123");
        jobStatus.setStatus(status);
        jobStatus.setDataType("USERS");
        jobStatus.setUserId("123");
        jobStatus.setUserName("testuser");
        jobStatus.setMessage("テストメッセージ");
        jobStatus.setCreatedAt(new Date());
        jobStatus.setUpdatedAt(new Date());
        
        if ("COMPLETED".equals(status)) {
            jobStatus.setDownloadUrl("https://example.com/download/export.zip");
            jobStatus.setOutputFileName("users_export_20231201.zip");
            jobStatus.setFileSize(1024L);
            jobStatus.setCompletedAt(new Date());
//            jobStatus.setS3Key("exports/2023-12-01/users_export_20231201.zip");
            jobStatus.setTotalRecords(100);
            jobStatus.setExportedRecords(100);
            jobStatus.setCsvFileCount(1);
        }
        
        return jobStatus;
    }

    /**
     * テスト用ImportJobStatusを作成
     */
    public static ImportJobStatus createTestImportJobStatus(String status) {
        ImportJobStatus jobStatus = new ImportJobStatus();
        jobStatus.setJobId("import_job_123");
        jobStatus.setStatus(status);
        jobStatus.setDataType("USERS");
        jobStatus.setUserId("123");
        jobStatus.setUserName("testuser");
        jobStatus.setS3Key("test/import/users.csv");
        jobStatus.setMessage("テストメッセージ");
        jobStatus.setCreatedAt(new Date());
        jobStatus.setUpdatedAt(new Date());
        
        if ("COMPLETED".equals(status)) {
            jobStatus.setInsertedCount(50);
            jobStatus.setUpdatedCount(30);
            jobStatus.setFailedCount(5);
            jobStatus.setTotalCount(85);
            jobStatus.setProcessedCount(85);
            jobStatus.setCompletedAt(new Date());
            jobStatus.setExecutionTimeMs(5000L);
            jobStatus.setErrorFileName("import_errors.csv");
            jobStatus.setErrorFileUrl("https://example.com/errors/import_errors.csv");
        }
        
        return jobStatus;
    }

    /**
     * テスト用ExportResultを作成
     */
    public static ExportResult createTestExportResult() {
        ExportResult result = new ExportResult();
        result.setFilePath(Paths.get("/tmp/test_export.csv"));
//        result.setRecordCount(100);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("exportedRecords", 100);
        statistics.put("totalRecords", 100);
        statistics.put("executionTimeMs", 3000L);
        result.setStatistics(statistics);
        
        return result;
    }

    /**
     * テスト用ImportResultを作成
     */
    public static ImportResult createTestImportResult() {
        ImportResult result = new ImportResult();
        result.setInsertedCount(50);
        result.setUpdatedCount(30);
        result.setFailedCount(5);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalRecords", 85);
        statistics.put("processedRecords", 85);
        statistics.put("executionTimeMs", 5000L);
        statistics.put("errorFileName", "import_errors.csv");
        statistics.put("errorFileUrl", "https://example.com/errors/import_errors.csv");
        statistics.put("errorFileS3Key", "errors/import_errors.csv");
        result.setStatistics(statistics);
        
        return result;
    }

    /**
     * テスト用UserActivationRequestを作成
     */
    public static UserActivationRequest createTestUserActivationRequest() {
        UserActivationRequest request = new UserActivationRequest();
        request.setEmployeeCode("EMP001");
        request.setEmail("<EMAIL>");
        request.setDepartmentCode("IT");
        return request;
    }

    /**
     * テスト用UserLoginRequestを作成
     */
    public static UserLoginRequest createTestUserLoginRequest() {
        UserLoginRequest request = new UserLoginRequest();
        request.setEmployeeCode("EMP001");
        request.setPassword("Password123!");
        request.setRememberMe(false);
        return request;
    }

    /**
     * テスト用PasswordResetRequestを作成
     */
    public static PasswordResetRequest createTestPasswordResetRequest() {
        PasswordResetRequest request = new PasswordResetRequest();
        request.setEmployeeCode("EMP001");
        request.setEmail("<EMAIL>");
        return request;
    }

    /**
     * テスト用SetPasswordRequestを作成
     */
    public static SetPasswordRequest createTestSetPasswordRequest() {
        SetPasswordRequest request = new SetPasswordRequest();
        request.setToken("test_token_123");
        request.setPassword("NewPassword123!");
        request.setConfirmPassword("NewPassword123!");
        return request;
    }

    /**
     * テスト用UserAuthResponseを作成
     */
    public static UserAuthResponse createTestUserAuthResponse() {
        return UserAuthResponse.builder()
            .userId(123L)
            .username("testuser")
            .email("<EMAIL>")
            .fullName("Test User")
            .roleId(1L)
            .roleName("USER")
            .departmentId(1L)
            .departmentName("IT部")
            .status("ACTIVE")
            .accessToken("access_token_123")
            .refreshToken("refresh_token_123")
            .tokenType("Bearer")
            .expiresIn(28800)
            .build();
    }

    /**
     * テスト用Userエンティティを作成
     */
    public static User createTestUser() {
        User user = new User();
        user.setId(123L);
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setFullName("Test User");
        user.setEmployeeCode("EMP001");
        user.setDepartmentId(1L);
        user.setDepartmentName("IT部");
        user.setRoleId(1L);
        user.setRoleName("USER");
        user.setStatus("ACTIVE");
        user.setArea("TOKYO");
        user.setPasswordHash("hashed_password");
        user.setCreatedAt(new Date());
        user.setUpdatedAt(new Date());
        return user;
    }

    /**
     * テスト用UserTokenエンティティを作成
     */
    public static UserToken createTestUserToken(String tokenType) {
        UserToken token = new UserToken();
        token.setId(1L);
        token.setUserId(123L);
        token.setToken("test_token_123");
        token.setTokenType(tokenType);
        token.setExpiresAt(new Date(System.currentTimeMillis() + 3600000)); // 1時間後
        token.setUsed(false);
        token.setCreatedAt(new Date());
        token.setUpdatedAt(new Date());
        return token;
    }

    /**
     * 期限切れのUserTokenを作成
     */
    public static UserToken createExpiredUserToken(String tokenType) {
        UserToken token = createTestUserToken(tokenType);
        token.setExpiresAt(new Date(System.currentTimeMillis() - 3600000)); // 1時間前
        return token;
    }

    /**
     * 使用済みのUserTokenを作成
     */
    public static UserToken createUsedUserToken(String tokenType) {
        UserToken token = createTestUserToken(tokenType);
        token.setUsed(true);
        token.setUsedAt(new Date());
        return token;
    }
}
