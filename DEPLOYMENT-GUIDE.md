# ユーザー認証機能デプロイメントガイド

## 概要

このガイドでは、実装されたユーザー認証機能のデプロイメント手順について説明します。

## 前提条件

- PostgreSQL データベース
- AWS Lambda 環境
- Amazon SES（メール送信用）
- AWS Secrets Manager（データベース認証情報用）

## デプロイメント手順

### 1. データベースセットアップ

#### 1.1 データベーススキーマの作成

```bash
# PostgreSQL に接続
psql -h your-db-host -U your-username -d your-database

# DDLスクリプトを実行
\i database/user_auth_tables.sql
```

#### 1.2 初期データの確認

スクリプト実行後、以下のテーブルが作成されます：
- `users` (拡張済み)
- `user_tokens` (新規)
- `departments` (新規)
- `roles` (新規)
- `user_sessions` (新規)

サンプルユーザーデータも挿入されます：
```sql
-- サンプルユーザーの確認
SELECT employee_code, email, full_name, status FROM users;
```

### 2. 環境変数の設定

#### 2.1 Lambda 環境変数

```bash
# データベース接続
DB_SECRET_NAME=your-db-secret-name

# JWT設定
JWT_SECRET_KEY=your-super-secret-jwt-key-here

# メール設定
DEFAULT_FROM_EMAIL=<EMAIL>

# フロントエンド設定
FRONTEND_BASE_URL=https://your-frontend-domain.com

# AWS設定
AWS_REGION=ap-northeast-1
```

#### 2.2 Secrets Manager の設定

データベース認証情報を Secrets Manager に保存：

```json
{
  "host": "your-db-host",
  "port": "5432",
  "dbname": "your-database",
  "username": "your-username",
  "password": "your-password"
}
```

### 3. Amazon SES の設定

#### 3.1 送信者メールアドレスの検証

```bash
# AWS CLI を使用してメールアドレスを検証
aws ses verify-email-identity --email-address <EMAIL>
```

#### 3.2 SES 権限の設定

Lambda 実行ロールに以下の権限を追加：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ses:SendEmail",
                "ses:SendRawEmail"
            ],
            "Resource": "*"
        }
    ]
}
```

### 4. Lambda デプロイメント

#### 4.1 ビルド

```bash
# プロジェクトをビルド
mvn clean package

# JAR ファイルの確認
ls -la target/*.jar
```

#### 4.2 Lambda 関数の更新

```bash
# AWS CLI を使用してデプロイ
aws bp update-function-code \
  --function-name your-bp-function-name \
  --zip-file fileb://target/your-jar-file.jar
```

### 5. API Gateway の設定

#### 5.1 新しいエンドポイントの追加

以下のエンドポイントを API Gateway に追加：

```
POST /api/auth/activation/request
POST /api/auth/activation/complete
POST /api/auth/login
POST /api/auth/password-reset/request
POST /api/auth/password-reset/complete
GET  /api/auth/user/{userId}
GET  /api/auth/health
```

#### 5.2 CORS の設定

認証エンドポイントに CORS を設定：

```json
{
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization"
}
```

## 動作確認

### 1. ヘルスチェック

```bash
curl -X GET https://your-api-domain/api/auth/health
```

期待される応答：
```json
{
  "code": 0,
  "data": "認証サービスは正常に動作しています",
  "msg": ""
}
```

### 2. ユーザーアクティベーション

#### 2.1 アクティベーション要求

```bash
curl -X POST https://your-api-domain/api/auth/activation/request \
  -H "Content-Type: application/json" \
  -d '{
    "employeeCode": "EMP001",
    "email": "<EMAIL>"
  }'
```

#### 2.2 メール確認

送信されたメールを確認し、アクティベーションリンクをクリック

#### 2.3 パスワード設定

```bash
curl -X POST https://your-api-domain/api/auth/activation/complete \
  -H "Content-Type: application/json" \
  -d '{
    "token": "activation-token-from-email",
    "password": "NewPassword123!",
    "confirmPassword": "NewPassword123!"
  }'
```

### 3. ログイン

```bash
curl -X POST https://your-api-domain/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "employeeCode": "EMP001",
    "password": "NewPassword123!"
  }'
```

期待される応答：
```json
{
  "code": 0,
  "data": {
    "userId": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "accessToken": "eyJ...",
    "refreshToken": "eyJ...",
    "tokenType": "Bearer",
    "expiresIn": 28800
  },
  "msg": ""
}
```

## トラブルシューティング

### よくある問題と解決方法

#### 1. メールが送信されない

**症状**: アクティベーションメールが届かない

**解決方法**:
- SES の送信者メールアドレスが検証済みか確認
- Lambda 実行ロールに SES 権限があるか確認
- SES のサンドボックス制限を確認

#### 2. データベース接続エラー

**症状**: `データベースエラーが発生しました`

**解決方法**:
- Secrets Manager の認証情報を確認
- VPC 設定とセキュリティグループを確認
- データベースの接続制限を確認

#### 3. JWT トークンエラー

**症状**: `無効な認証トークンです`

**解決方法**:
- JWT_SECRET_KEY 環境変数を確認
- トークンの有効期限を確認
- フロントエンドのトークン送信方法を確認

### ログの確認

CloudWatch Logs でエラーログを確認：

```bash
# 最新のログを確認
aws logs tail /aws/bp/your-function-name --follow
```

## セキュリティ考慮事項

### 1. 本番環境での設定

- サンプルユーザーデータを削除
- 強力な JWT シークレットキーを設定
- データベースアクセスを制限
- HTTPS のみを使用

### 2. 監視とアラート

- 認証失敗の監視
- 異常なトークン使用の検出
- メール送信失敗の監視

### 3. 定期メンテナンス

期限切れトークンのクリーンアップ：

```sql
-- 期限切れトークンの削除（週次実行推奨）
SELECT cleanup_expired_tokens();
```

## 次のステップ

1. **監視の設定**: CloudWatch アラームの設定
2. **バックアップ**: データベースバックアップの設定
3. **スケーリング**: 負荷に応じた Lambda 設定の調整
4. **セキュリティ強化**: WAF や API レート制限の設定

## サポート

問題が発生した場合は、以下を確認してください：

1. [README-USER-AUTH.md](README-USER-AUTH.md) - 機能詳細
2. CloudWatch Logs - エラーログ
3. データベースの統計情報ビュー: `SELECT * FROM user_auth_stats;`

---

**注意**: 本番環境にデプロイする前に、必ずテスト環境で動作確認を行ってください。
