# 設定管理システム

このプロジェクトでは、環境変数から設定ファイルベースの設定管理システムに移行しました。

## 概要

従来の`System.getenv()`による環境変数取得から、設定ファイルベースの管理に変更し、以下の利点を実現しています：

- **環境別設定**: 開発、テスト、本番環境ごとに異なる設定ファイル
- **設定の可視化**: 設定値がファイルで管理され、変更履歴が追跡可能
- **環境変数での上書き**: 必要に応じて環境変数で設定値を上書き可能
- **型安全性**: 文字列、整数、ブール値の型変換をサポート

## 設定ファイル構成

### 基本設定ファイル
- `application.properties` - デフォルト設定（開発環境）
- `application-test.properties` - テスト環境設定
- `application-prod.properties` - 本番環境設定

### 環境の切り替え
環境変数 `SPRING_PROFILES_ACTIVE` で環境を指定：

```bash
# 開発環境（デフォルト）
export SPRING_PROFILES_ACTIVE=dev

# テスト環境
export SPRING_PROFILES_ACTIVE=test

# 本番環境
export SPRING_PROFILES_ACTIVE=prod
```

## 設定項目

### AWS設定
```properties
# AWS基本設定
aws.region=ap-northeast-1
aws.s3.bucket.name=teams-budget-app-files-dev

# データベース設定
db.secret.name=dev-db-secret
db.host=localhost
db.port=5432
db.name=postgres
db.username=postgres
db.password=123456
```

### 認証設定
```properties
# JWT設定
jwt.secret.key=your-secret-key

# Azure AD設定
azure.client.id=your-client-id
azure.tenant.id=your-tenant-id
```

### メール設定
```properties
# メール設定
email.default.from=<EMAIL>
```

### フロントエンド設定
```properties
# フロントエンド設定
frontend.base.url=https://example.com
```

### ログ設定
```properties
# ログ設定
aws.lambda.log.format=TEXT
aws.lambda.log.level=DEBUG
```

## 使用方法

### 基本的な設定値取得
```java
ConfigurationManager config = ConfigurationManager.getInstance();

// 基本的な取得
String region = config.getProperty("aws.region");

// デフォルト値付き取得
String bucketName = config.getProperty("aws.s3.bucket.name", "default-bucket");

// 必須設定の取得（値がない場合は例外）
String secretKey = config.getRequiredProperty("jwt.secret.key");
```

### 型変換
```java
// 整数値の取得
int port = config.getIntProperty("db.port", 5432);

// ブール値の取得
boolean enabled = config.getBooleanProperty("feature.enabled", false);
```

### 設定値の存在確認
```java
if (config.hasProperty("optional.setting")) {
    // 設定が存在する場合の処理
}
```

## 環境変数での上書き

設定ファイルの値は環境変数で上書きできます。キーの変換ルール：

- ドット（`.`）をアンダースコア（`_`）に変換
- 全て大文字に変換

例：
```bash
# 設定ファイル: aws.region=ap-northeast-1
# 環境変数での上書き:
export AWS_REGION=us-west-2

# 設定ファイル: jwt.secret.key=default-key
# 環境変数での上書き:
export JWT_SECRET_KEY=production-secret-key
```

## 移行された設定

以下のクラスで`System.getenv()`から設定管理器に移行しました：

### インフラストラクチャ層
- **S3Service**: AWS_REGION, S3_BUCKET_NAME
- **EmailService**: AWS_REGION, DEFAULT_FROM_EMAIL
- **AuroraConnectionManager**: DB_SECRET_NAME, AWS_REGION, DB_HOST, DB_PORT, DB_NAME, DB_USERNAME, DB_PASSWORD

### セキュリティ
- **JwtTokenGenerator**: JWT_SECRET_KEY
- **AuthMiddleware**: CLIENT_ID, TENANT_ID

### ドメインサービス
- **PasswordResetService**: FRONTEND_BASE_URL
- **UserActivationService**: FRONTEND_BASE_URL

## 本番環境での設定

### 機密情報の管理
本番環境では以下の設定を適切に設定してください：

```properties
# 本番環境設定例
jwt.secret.key=
azure.client.id=
azure.tenant.id=
db.password=
```

これらの値は環境変数で設定することを推奨します：

```bash
export JWT_SECRET_KEY="your-production-secret-key"
export AZURE_CLIENT_ID="your-production-client-id"
export AZURE_TENANT_ID="your-production-tenant-id"
export DB_PASSWORD="your-production-db-password"
```

### AWS Lambda環境での設定
Lambda関数の環境変数として設定：

```yaml
Environment:
  Variables:
    SPRING_PROFILES_ACTIVE: prod
    JWT_SECRET_KEY: !Ref JwtSecretKey
    AZURE_CLIENT_ID: !Ref AzureClientId
    AZURE_TENANT_ID: !Ref AzureTenantId
```

## デバッグ

### 設定値の確認
開発時に設定値を確認するには：

```java
ConfigurationManager config = ConfigurationManager.getInstance();
config.logConfiguration(); // 機密情報以外の設定をログ出力
```

### 初期化状態の確認
```java
boolean initialized = ConfigurationInitializer.isInitialized();
```

## 注意事項

1. **機密情報**: パスワード、シークレットキー、トークンなどの機密情報は設定ファイルに直接記載せず、環境変数で設定してください。

2. **設定の優先順位**: 環境変数 > 環境固有設定ファイル > デフォルト設定ファイル

3. **初期化**: `ConfigurationInitializer.initialize()`は`LambdaHandler`のコンストラクタで自動的に呼び出されます。

4. **テスト**: テスト時は`ConfigurationInitializer.reset()`でリセットできます。
